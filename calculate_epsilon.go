package main

import (
	"fmt"
	"math"
)

func main() {
	// 计算 expN(2.7182818,-20) = math.Pow(2.7182818, -20)
	epsilon := math.Pow(2.7182818, -20)
	
	fmt.Printf("expN(2.7182818,-20) = math.Pow(2.7182818, -20)\n")
	fmt.Printf("结果: %.20e\n", epsilon)
	fmt.Printf("结果: %.30f\n", epsilon)
	
	// 对比一下自然常数 e 的 -20 次方
	eEpsilon := math.Pow(math.E, -20)
	fmt.Printf("\n对比 math.E^(-20):\n")
	fmt.Printf("math.E = %.15f\n", math.E)
	fmt.Printf("math.Pow(math.E, -20) = %.20e\n", eEpsilon)
	
	// 差异
	fmt.Printf("\n差异:\n")
	fmt.Printf("2.7182818^(-20) = %.20e\n", epsilon)
	fmt.Printf("math.E^(-20)    = %.20e\n", eEpsilon)
	fmt.Printf("差异            = %.20e\n", math.Abs(epsilon - eEpsilon))
}
