package abtest

func GetLTRV1ExpString(abParamClient *SearchParamMultiClient) string {
	return abParamClient.GetParamWithString("Search.MultiFactor.LTRV1ExpString", "")
}

func GetLTRV1ExpParameters(abParamClient *SearchParamMultiClient) string {
	return abParamClient.GetParamWithString("Search.MultiFactor.LTRV1ExpParameters", "")
}

func GetIsUseStaticLTRV1(abParamClient *SearchParamMultiClient) bool {
	return getBool(abParamClient.GetParamWithInt("Search.MultiFactor.UseStaticLTRV1", 0))
}
