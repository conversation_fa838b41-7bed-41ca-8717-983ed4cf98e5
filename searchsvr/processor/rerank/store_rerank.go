package rerank

import (
	"context"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func StoresReRank(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model.StoreInfo) []*model.StoreInfo {
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseReRank, time.Since(pt))
	}()
	// 干预重排
	// todo: delete it
	if abtest.GetIsPrintStoreIds(traceInfo.AbParamClient) {
		logkit.FromContext(ctx).Info("ForDebug Intervention before storeIds", logkit.Uint64s("storeIds", model.GetStoreIds(stores)))
	}

	stores = Intervention(ctx, traceInfo, stores)
	// todo: delete it
	if abtest.GetIsPrintStoreIds(traceInfo.AbParamClient) {
		logkit.FromContext(ctx).Info("ForDebug Intervention after storeIds", logkit.Uint64s("storeIds", model.GetStoreIds(stores)))
	}

	// Ltr
	stores = LtrReRank(ctx, traceInfo, stores)
	// top K 门店相关性低的打压
	stores = DeboostReRank(ctx, traceInfo, stores, DeboostRelevance)
	// top K 门店 category跟 query 不相关的打压
	stores = DeboostReRank(ctx, traceInfo, stores, DeboostCategory)

	return stores
}
