package rerank

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	predictor "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/predictor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/Knetic/govaluate"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
)

var epsilon = math.Pow(2.7182818284, -20)

type GeneratorType string

const (
	GeneratorTypeOriginal       GeneratorType = "original"
	GeneratorTypeMMR            GeneratorType = "mmr"
	GeneratorTypeFormula        GeneratorType = "formula"          // 动态表达式
	GeneratorTypeFormulaStatic1 GeneratorType = "formula-static-1" // 固定公式1
	GeneratorTypeFormulaStatic2 GeneratorType = "formula-static-2" // 固定公式2
)

type EvaluatorType string

const (
	EvaluatorTypeReduceSum  EvaluatorType = "reduce_sum"  // item 维度T个打分，多条队列，单个item融合算分，加和得到列表分数，取max队列
	EvaluatorTypeDirectSort EvaluatorType = "direct_sort" // item 维度T个打分， 1条队列重新item融合算分后重排序
	EvaluatorTypeDirectOut  EvaluatorType = "direct_out"  // list 维度T个打分，每条队列融合算分取max队列
)

func ListWiseReRank(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo, storeInfos []*model.StoreInfo) []*model.StoreInfo {
	if decision.IsNeedListWise(ctx, traceInfo) == false {
		return storeInfos
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseLWRank, time.Since(pt))
	}()
	// 选取候选集N个,支持后续翻页，默认100
	storeSet := GetCandidateStores(ctx, traceInfo, storeInfos) // 完成
	// 生成K个候选序列，每个序列M个，默认20
	storeLists := GenerateStoreLists(ctx, traceInfo, storeSet) // original, formula, mmr
	debugInfo.FillProcessInfoWithListWise(traceInfo, storeLists)
	// 评估各个候选序列分数，选取最优序列
	storeList := EvaluateStoreLists(ctx, traceInfo, storeLists) //
	// listwise ack dump
	goroutine.WithGo(ctx, "ListWiseAck", func(params ...interface{}) {
		ListWiseAck(ctx, traceInfo, storeList)
	})
	// 选择最新的重新填充storeInfos
	storeInfos = ListWiseReFilling(ctx, traceInfo, storeInfos, storeList)
	return storeInfos
}

// 按照挑选的storeList顺序重组storeInfos
func ListWiseReFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos, storeList *model.StoreList) []*model.StoreInfo {
	if storeList == nil || storeList.StoresFinal == nil || len(storeList.StoresFinal) == 0 {
		return storeInfos
	}
	nStore := len(storeInfos)
	resStores := make(model.StoreInfos, 0, nStore)
	existMap := make(map[uint64]struct{}, len(storeList.StoresFinal))
	// 1. 填充前面N页数据， 第一页为0
	begin := int((traceInfo.TraceRequest.PageNum - 1) * traceInfo.TraceRequest.PageSize) // pageNum 从1开始
	index := 0
	for ; index < begin; index++ {
		if storeInfos[index] == nil {
			continue
		}
		resStores = append(resStores, storeInfos[index])
		existMap[storeInfos[index].StoreId] = struct{}{}
	}
	// 2.填充storeList
	for _, store := range storeList.StoresFinal {
		resStores = append(resStores, store)
		existMap[store.StoreId] = struct{}{}
	}
	// 3. 剩下的按顺序填充storeInfos
	for ; index < nStore; index++ {
		if storeInfos[index] == nil {
			continue
		}
		if _, ok := existMap[storeInfos[index].StoreId]; ok {
			continue
		}
		resStores = append(resStores, storeInfos[index])
		existMap[storeInfos[index].StoreId] = struct{}{}
	}
	return resStores
}

func ListWiseAck(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeList *model.StoreList) {
	if storeList == nil || len(storeList.StoresFinal) == 0 || len(storeList.GeneratorConf.Name) == 0 {
		logkit.FromContext(ctx).Error("store list is empty, skip listwise ack")
		return
	}
	if len(storeList.SpexInstanceId) == 0 {
		logkit.FromContext(ctx).Error("SpexInstanceId is empty, skip listwise ack")
		return
	}
	itemIds := make([]uint64, 0)
	itemFeatures := make([][]byte, 0)
	genName := storeList.GeneratorConf.Name
	pos := int64((traceInfo.TraceRequest.PageNum - 1) * traceInfo.TraceRequest.PageSize) // pageNum 从1开始
	scoreKeys := strings.Split(traceInfo.EvaluateConf.ScoreKeys, ",")
	scoresMap := make(map[string][]float64)
	itemMap := storeList.ListWiseItems.ListWiseItemMap()
	for _, store := range storeList.StoresFinal {
		pos++ // 记录item从第0也开始的顺序，从1开始
		// 广告门店跳过ack, 但是pos得算上
		if store.ItemFeature.GetCIStoreType() == model.StoreTypeAds {
			continue
		}
		itemIds = append(itemIds, store.StoreId)
		itemFeature := &food.AfterItemFeature{
			CIGeneratorName: genName,
			CIFinalPosition: pos,
		}
		data, _ := proto.Marshal(itemFeature)
		itemFeatures = append(itemFeatures, data)
		if traceInfo.EvaluateConf.ScoreType == apollo.EvaluatorScoreTypeItem {
			for _, key := range scoreKeys {
				item := itemMap[store.StoreId]
				if item == nil || item.ItemParams == nil {
					continue
				}
				score, exist2 := item.ItemParams[key]
				if !exist2 {
					continue
				}
				if len(scoresMap[key]) == 0 {
					scoresMap[key] = make([]float64, 0, storeList.StoresFinal.Len())
				}
				scoresMap[key] = append(scoresMap[key], score.(float64))
			}
		}
	}

	if traceInfo.EvaluateConf.ScoreType == apollo.EvaluatorScoreTypeList {
		for _, key := range scoreKeys {
			score, exist1 := storeList.ListEvaluateParams[key]
			if !exist1 {
				continue
			}
			if len(scoresMap[key]) == 0 {
				scoresMap[key] = make([]float64, 0, storeList.StoresFinal.Len())
			}
			scoresMap[key] = append(scoresMap[key], score.(float64))

		}
	}
	modeScoreMapTemp := make(map[string]*predictor.ScoreList)
	for key, scores := range scoresMap {
		modeScoreMapTemp[key+"_scores"] = &predictor.ScoreList{
			Scores: scores,
		}
	}
	modScoresInfo := make(map[string]*predictor.ScoresInfo)
	modScoresInfo[traceInfo.EvaluateConf.ModelName] = &predictor.ScoresInfo{
		Scores: modeScoreMapTemp,
	}
	req := &predictor.PreAckReq{
		Reqid:         storeList.ReqId,
		Itemids:       itemIds,
		ItemFeas:      itemFeatures,
		ModScoresInfo: modScoresInfo,
		ModelName:     traceInfo.EvaluateConf.ModelName,
		LwChoiceIndex: storeList.ListOriginalIndex,
	}
	startTime := time.Now()
	var resp *predictor.PreAckRsp
	var err error
	resp, err = mlplatform.PredictAck(ctx, req, storeList.SpexInstanceId, storeList.SpexInstanceId, traceInfo.IsDebug)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "listwise_predict_ack"), zap.String("cost", time.Since(startTime).String()))
	if traceInfo.IsDebug {
		reqStr, _ := json.Marshal(req)
		rspStr, _ := json.Marshal(resp)
		logkit.FromContext(ctx).Info("Listwise PredictAck", zap.String("req", string(reqStr)), zap.String("resp", string(rspStr)))
	}
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "listwise_predict_ack")
	if traceInfo.IsDebug {
		logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:listwise predict ack", logkit.Any("request", req), logkit.Any("response", resp))
	}
	if err != nil {
		logkit.FromContext(ctx).Error("failed to listwise predict ack", logkit.String("cost", time.Since(startTime).String()), logkit.Any("err", err), logkit.Any("reqId", storeList.ReqId))
		metric_reporter.ReportClientRequestError(1, "listwise_predict_ack", "failed")
		return
	}
	metric_reporter.ReportClientRequestError(1, "listwise_predict_ack", "0")
	if resp == nil || resp.GetErrcode() != 0 {
		logkit.FromContext(ctx).Error("failed to listwise predict ack", logkit.Any("ListwiseModuleName", traceInfo.EvaluateConf.ModelName), logkit.Any("reqId", storeList.ReqId), logkit.Any("resp", resp))
		return
	}
	return
}

// 根据预估分数选取最优序列
func ListWiseEvaluatorSelect(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeLists []*model.StoreList, evaluatorConf *apollo.ListWiseEvaluatorConfig, scoreMap map[string]*predictor.ScoreList) *model.StoreList {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseLWRankEvaluatorSelect, time.Since(pt))
	}()
	// 根据配置和返回预估信息，计算序列分数
	var storeList *model.StoreList
	switch evaluatorConf.Type {
	case string(EvaluatorTypeReduceSum): // item => count => sum => max list
		storeList = EvaluateReduceSumAndSort(ctx, traceInfo, storeLists, evaluatorConf, scoreMap, false)
	case string(EvaluatorTypeDirectSort): // item => count => sum => max list => resort
		storeList = EvaluateReduceSumAndSort(ctx, traceInfo, storeLists, evaluatorConf, scoreMap, true)
	case string(EvaluatorTypeDirectOut): // list => count => max list
		storeList = EvaluateDirectOut(ctx, traceInfo, storeLists, evaluatorConf, scoreMap)
	default:
		logkit.FromContext(ctx).Error("evaluator type not supported, return first list", zap.Any("evaluatorConf", evaluatorConf))
		storeList = storeLists[0]
	}
	storeList.IsSelected = true
	return storeList
}

// list => count => max list
func EvaluateDirectOut(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeLists model.StoreLists, evaluatorConf *apollo.ListWiseEvaluatorConfig, scoreMap map[string]*predictor.ScoreList) *model.StoreList {
	expression := util.BuildExpression(ctx, evaluatorConf.Formula)
	// 公共权重参数填充，w1~w7
	parameters := util.BuildExpParameters(evaluatorConf.Parameters)
	scoreKeys := strings.Split(evaluatorConf.ScoreKeys, ",")
	// 计算列表分数，用于排序
	for i, storeList := range storeLists {
		// 每个序列分数填充，计算得到本序列分数
		for _, key := range scoreKeys {
			scoreList, exist := scoreMap[key]
			if !exist {
				continue
			}
			parameters[key] = scoreList.GetScores()[i]
		}
		storeList.ListEvaluateParams = parameters
		// 这里只用list 分数参与计算
		score, _ := util.EvaluateScore(ctx, expression, parameters)
		storeList.ListEvaluateExpStr = expression.String()
		storeList.ListEvaluateScore = score
	}
	// list 分数排序之后，返回最高分数的序列
	storeLists = storeLists.SortByListEvaluateScore()
	var index uint64 = 0
	for _, list := range storeLists {
		list.ListSortedIndex = index
		index++
	}
	return storeLists[0]
}

// item => count => sum => max list => (resort)
func EvaluateReduceSumAndSort(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeLists model.StoreLists, evaluatorConf *apollo.ListWiseEvaluatorConfig, scoreMap map[string]*predictor.ScoreList, isNeedReSort bool) *model.StoreList {
	expression := util.BuildExpression(ctx, evaluatorConf.Formula)
	scoreKeys := strings.Split(evaluatorConf.ScoreKeys, ",")
	// 计算列表分数，用于排序
	for i, storeList := range storeLists {
		itemMap := storeList.ListWiseItems.ListWiseItemMap()
		for j, store := range storeList.StoresPredict {
			// 公共权重参数填充，w1~w7
			parameters := util.BuildExpParameters(evaluatorConf.Parameters)
			// 填充listwise 预估分数作为融合因子, reduce sum 返回的模型分数是item维度，且每个item
			for _, key := range scoreKeys {
				scoreList, exist := scoreMap[key]
				if !exist {
					continue
				}
				scoreLen := int(scoreList.GetLen())
				if i*scoreLen+j < len(scoreList.GetScores()) {
					parameters[key] = scoreList.GetScores()[i*scoreLen+j]
				} else {
					logkit.FromContext(ctx).Error("EvaluateReduceSum failed to get score from model", logkit.String("key", key), logkit.Uint64("len", scoreList.GetLen()),
						zap.Any("scores", scoreList.GetScores()), zap.Int("i", i), zap.Int("j", j))
				}
			}
			// 填充listwise 相关因子
			store.BuildListWiseStoreParams(parameters)
			parameters["item_evaluator_pos"] = j + 1 // 为evaluator 单独添加一个参数，用于区分item位置
			score, _ := util.EvaluateScore(ctx, expression, parameters)
			item := itemMap[store.StoreId]
			if item == nil {
				item = &model.ListWiseItem{
					StoreId: store.StoreId,
				}
				storeList.ListWiseItems = append(storeList.ListWiseItems, item)
			}
			item.EvaluatorScore = score
			item.ItemParams = parameters
			// 序列的分数是各个item的融合分数加和
			storeList.ListEvaluateScore += score
		}
	}
	storeLists = storeLists.SortByListEvaluateScore()
	var index uint64 = 0
	for _, list := range storeLists {
		list.ListSortedIndex = index
		index++
	}
	storeList := storeLists[0]
	if isNeedReSort {
		storeList = rerankWithEvaluatorScores(storeList)
	}
	return storeList
}

// 会改变序列中顺序。在原始队列中保持广告、干预门店位置不变，根据最新排序填充其他门店，得到最终门店列表
func rerankWithEvaluatorScores(storeList *model.StoreList) *model.StoreList {
	items := make(model.ListWiseItems, 0, len(storeList.StoresOriginal))
	itemMap := storeList.ListWiseItems.ListWiseItemMap()
	for _, store := range storeList.StoresOriginal {
		if store == nil {
			continue
		}
		if store.ItemFeature.GetCIStoreType() == model.StoreTypeNormal && store.ExactMatch == 0 {
			items = append(items, itemMap[store.StoreId])
		}
	}
	items.SortByEvaluatorScore()

	// 改下原本顺序
	storeFinal := make(model.StoreInfos, 0)
	j := 0
	storeMap := storeList.StoresOriginal.StoreInfoMap()
	for _, store := range storeList.StoresOriginal {
		if store == nil {
			continue
		}
		// 干预和广告门店顺序不变
		if store.ItemFeature.GetCIStoreType() == model.StoreTypeAds || store.ItemFeature.GetCIStoreType() == model.StoreTypeInter || store.ExactMatch > 0 {
			storeFinal = append(storeFinal, store)
			continue
		}
		if j < len(items) && items[j] != nil && storeMap[items[j].StoreId] != nil {
			storeFinal = append(storeFinal, storeMap[items[j].StoreId])
		}
		j++
	}
	storeList.StoresFinal = storeFinal
	return storeList
}

// K个候选序列预估打分
func EvaluateStoreLists(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeLists model.StoreLists) *model.StoreList {
	if storeLists == nil || len(storeLists) == 0 || storeLists[0] == nil || len(storeLists[0].StoresOriginal) == 0 {
		return &model.StoreList{}
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseLWRankEvaluateStoreLists, time.Since(pt))
	}()
	//获取各个候选序列的门店全集
	storesAll := storeLists.AllDistinctStores()
	traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenLWDistinct, len(storesAll))
	// build item feature
	itemFeas, itemIds := buildFeaturesForLiseWise(ctx, traceInfo, storesAll)
	// build ItemList 特征
	for _, storeList := range storeLists {
		nStores := len(storeList.StoresOriginal)
		storeList.StoresFinal = storeList.StoresOriginal               // DirectSort 会改变序列中顺序, 后续重新赋值，其他和StoresOriginal保持一样
		storeList.StoresPredict = make([]*model.StoreInfo, 0, nStores) // 去除广告门店，得到普通门店列表用于后续预估，保持原有顺序
		storeList.StoresStatic = make([]*model.StoreInfo, 0, nStores)  // 去除广告门店，得到普通门店列表用于计算排序，不影响原始顺序
		for _, store := range storeList.StoresOriginal {
			if store.ItemFeature.GetCIStoreType() == model.StoreTypeAds {
				continue
			}
			storeList.StoresPredict = append(storeList.StoresPredict, store)
			storeList.StoresStatic = append(storeList.StoresStatic, store)
		}
		storeList.BuildLiseWiseItemFeatures(ctx, traceInfo)
		storeList.BuildLiseWiseContextFeatures()
	}
	itemLists := buildPredictItemList(ctx, traceInfo, storeLists)

	// build context 特征
	AddContextFeatureForListWise(ctx, traceInfo, len(storeLists[0].StoresPredict))

	evaluatorName := traceInfo.AbParamClient.GetParamWithString("Search.LWReRank.EvaluatorName", "")
	evaluatorConf := apollo.AlgoApolloCfg.ListWiseEvaluatorMap.GetListWiseEvaluator(evaluatorName)
	traceInfo.EvaluateConf = evaluatorConf
	if len(evaluatorName) <= 0 || evaluatorConf == nil || len(evaluatorConf.Type) == 0 {
		// evaluator 异常，降级使用第一个队列返回
		logkit.FromContext(ctx).Error("EvaluatorName is empty, use the first store list", zap.String("evaluatorName", evaluatorName))
		return storeLists[0]
	}
	// 拼接req
	reqId := fmt.Sprintf("listwise_%s_%d", traceInfo.TraceRequest.PublishId, traceInfo.TraceRequest.PageNum)
	predictReq := &predictor.PredictReq{
		Uid:      traceInfo.UserId,
		CtxFea:   traceInfo.ContextFeatureBytes,
		Reqid:    reqId,
		Logid:    reqId,
		Models:   []string{evaluatorConf.ModelName},
		Itemids:  itemIds,
		ItemFeas: itemFeas,
		ItemList: itemLists,
	}
	resp, err := mlplatform.PredictV2(ctx, predictReq, evaluatorConf.ModelName, traceInfo.IsDebug)
	if err != nil {
		logkit.FromContext(ctx).Error("predict list wise error, use the first store list", zap.Error(err), zap.Any("model name", predictReq.GetModels()))
		traceInfo.SlaCode.UpdateErrorCode(sla_code.RANK_ERROR)
		return storeLists[0]
	}
	if resp.GetModScoresInfo() == nil || resp.GetModScoresInfo()[evaluatorConf.ModelName] == nil || len(resp.GetModScoresInfo()[evaluatorConf.ModelName].GetScores()) == 0 {
		logkit.FromContext(ctx).Error("predict parse scores failed, use the first store list", zap.Error(err), logkit.String("modelName", evaluatorConf.ModelName), zap.Any("resp", resp))
		return storeLists[0]
	}
	scoreMap := resp.GetModScoresInfo()[evaluatorConf.ModelName].GetScores()
	//Evaluator处理, 按照配置将分数赋值到store lists 中,挑选最终序列
	storeList := ListWiseEvaluatorSelect(ctx, traceInfo, storeLists, evaluatorConf, scoreMap)
	storeList.SpexInstanceId = resp.GetSpexInstanceId()
	storeList.ReqId = reqId
	return storeList
}

func buildFeaturesForLiseWise(ctx context.Context, traceInfo *traceinfo.TraceInfo, storesAll model.StoreInfos) ([][]byte, []uint64) {
	nStores := len(storesAll)
	itemIDs := make([]uint64, nStores)
	itemFeatures := make([][]byte, nStores)
	for i := 0; i < nStores; i++ {
		itemIDs[i] = storesAll[i].StoreId
		data, _ := proto.Marshal(storesAll[i].ItemFeature)
		itemFeatures[i] = data
		logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:buildFeaturesForLiseWise", logkit.Any("store id", storesAll[i].StoreId), logkit.Any("item", storesAll[i].ItemFeature))
	}
	return itemFeatures, itemIDs
}

func buildPredictItemList(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeLists model.StoreLists) []*predictor.ItemList {
	plist := make([]*predictor.ItemList, 0, len(storeLists))
	for _, storeList := range storeLists {
		plist = append(plist, &predictor.ItemList{
			ItemIds:  storeList.ListWiseItemIds,
			ItemCtxs: storeList.ListWiseItemFeatureBytes,
			ListCtx:  storeList.ListWiseContextFeatureBytes,
		})
	}
	return plist
}

func GetCandidateStores(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos) model.StoreInfos {
	candidateSize := traceInfo.AbParamClient.GetParamWithInt("Search.LWReRank.CandidateSize", 0)
	begin := int((traceInfo.TraceRequest.PageNum - 1) * traceInfo.TraceRequest.PageSize) // pageNum 从1开始
	if begin >= len(storeInfos) {
		logger.MyDebug(ctx, traceInfo.IsDebug, "GetCandidateStores not enough", logkit.Int("begin", begin), logkit.Int("size", len(storeInfos)))
		return []*model.StoreInfo{}
	}
	// 分页时直接跳过前面N页
	storeSet := make([]*model.StoreInfo, 0, candidateSize)
	cnt := 0
	for _, store := range storeInfos[begin:] {
		if store.ItemSceneType == foodalgo_search.ItemSceneType_NoFewItem {
			continue
		}
		storeSet = append(storeSet, store)
		cnt += 1
		if cnt >= candidateSize {
			break
		}
	}
	return storeSet
}

func GenerateStoreLists(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeSet model.StoreInfos) []*model.StoreList {
	if len(storeSet) == 0 {
		return model.StoreLists{}
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseLWRankGenerateStoreLists, time.Since(pt))
	}()
	generatorNamesStr := traceInfo.AbParamClient.GetParamWithString("Search.LWReRank.GeneratorNames", "") // "generator_1,generator_2,generator_3,generator_4"
	if len(generatorNamesStr) <= 0 {
		return model.StoreLists{}
	}
	generatorNames := strings.Split(generatorNamesStr, ",")
	if len(generatorNames) <= 0 {
		return model.StoreLists{}
	}
	for _, store := range storeSet {
		store.StoreNameTerms = util.TextTerms(store.StoreName)
	}
	storeLists := make([]*model.StoreList, len(generatorNames), len(generatorNames))
	seqLen := traceInfo.AbParamClient.GetParamWithInt("Search.LWReRank.CandidateSeqLen", 2)
	if seqLen > len(storeSet) {
		seqLen = len(storeSet)
	}
	var index uint64 = 0
	wg := sync.WaitGroup{}
	for _, generatorName := range generatorNames {
		generatorConf := apollo.AlgoApolloCfg.ListWiseGeneratorMap.GetListWiseGenerator(generatorName)
		if generatorConf == nil {
			logkit.FromContext(ctx).Error("Generator config is nil, skip", logkit.String("config name", generatorName))
			continue
		}
		wg.Add(1)
		goroutine.WithGo(ctx, "GenList", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			oi := param[0].(uint64)
			config := param[1].(apollo.ListWiseGeneratorConfig)
			storeList := GenList(ctx, traceInfo, storeSet, seqLen, config)
			storeList.ListOriginalIndex = oi
			storeLists[oi] = storeList
		}, index, *generatorConf)
		index++
	}
	wg.Wait()

	// 避免有配置错误导致空序列
	res := make([]*model.StoreList, 0, len(generatorNames))
	for _, storeList := range storeLists {
		if storeList != nil && len(storeList.StoresOriginal) > 0 {
			res = append(res, storeList)
		}
	}
	return res
}

func GenList(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeSet model.StoreInfos, seqLen int, generatorConf apollo.ListWiseGeneratorConfig) *model.StoreList {
	var storeList *model.StoreList
	switch generatorConf.Type {
	case string(GeneratorTypeOriginal):
		storeList = GenOriginalList(ctx, traceInfo, storeSet, seqLen, generatorConf)
	case string(GeneratorTypeFormula), string(GeneratorTypeFormulaStatic1), string(GeneratorTypeFormulaStatic2):
		storeList = GenFormulaList(ctx, traceInfo, storeSet, seqLen, generatorConf)
	case string(GeneratorTypeMMR):
		storeList = GenMMRList(ctx, traceInfo, storeSet, seqLen, generatorConf)
	default:
		logkit.FromContext(ctx).Error("invalid strategy, use default sort", zap.Any("strategy", generatorConf))
		defaultConf := apollo.ListWiseGeneratorConfig{
			Name: "default_generator",
			Type: string(GeneratorTypeOriginal),
		}
		storeList = GenOriginalList(ctx, traceInfo, storeSet, seqLen, defaultConf)
	}
	return storeList
}

// 从候选集candidateStores 100个门店中，根据原始顺序，选取seqLen 20个门店作为候选集
func GenOriginalList(ctx context.Context, traceInfo *traceinfo.TraceInfo, candidateStores model.StoreInfos, seqLen int, generatorConf apollo.ListWiseGeneratorConfig) *model.StoreList {
	storeList := &model.StoreList{
		GeneratorConf: generatorConf,
	}
	if len(candidateStores) <= seqLen {
		storeList.StoresOriginal = candidateStores
	} else {
		storeList.StoresOriginal = candidateStores[:seqLen]
	}
	return storeList
}

// 从候选集candidateStores 100个门店中，根据融合公式计算得分排序，选取seqLen 20个门店作为候选序列，注意广告和干预门店位置不受影响
func GenFormulaList(ctx context.Context, traceInfo *traceinfo.TraceInfo, candidateStores model.StoreInfos, seqLen int, generatorConf apollo.ListWiseGeneratorConfig) *model.StoreList {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseLWRankGenFormulaList, time.Since(pt))
	}()
	// 选择非ads, inter 门店
	storeMapNormal := make(map[uint64]*model.StoreInfo, len(candidateStores)) // 普通门店，用于融合算分排序
	storesSelect := make(model.StoreInfos, seqLen)                            // 最后选中的门店
	itemsSelect := make([]*model.ListWiseItem, seqLen)
	// Deboost 打压需求，TopN，TopK 是包含广告和干预门店的
	config := abtest.GetDeboostConfig(traceInfo.IsDebug, traceInfo.AbParamClient)
	adsOrIntentStoreNum := 0
	for i, store := range candidateStores {
		// 1表示干预门店，值=2表示广告，值=3表示自然门店
		if store.ItemFeature.GetCIStoreType() == model.StoreTypeAds || store.ItemFeature.GetCIStoreType() == model.StoreTypeInter || store.ExactMatch > 0 {
			if i < seqLen {
				storesSelect[i] = store // 前 seqLen 的广告、干预门店，直接原位置保留
				itemsSelect[i] = &model.ListWiseItem{
					StoreId:   store.StoreId,
					StoreType: store.ItemFeature.GetCIStoreType(),
				}
			}
			if i+1 >= config.TopN && i+1 <= config.TopK {
				adsOrIntentStoreNum += 1
			}
			continue // 超过seqLen 的广告、干预门店，跳过不参与
		}
		storeMapNormal[store.StoreId] = store
	}
	// 计算分数，新增 generate score, 用于排序
	var items model.ListWiseItems
	switch generatorConf.Type {
	case string(GeneratorTypeFormula):
		items = FormulaExpression(ctx, generatorConf, storeMapNormal)
	case string(GeneratorTypeFormulaStatic1):
		items = FormulaStatic1(ctx, generatorConf, storeMapNormal)
	case string(GeneratorTypeFormulaStatic2):
		items = FormulaStatic2(ctx, generatorConf, storeMapNormal)
	default:
		items = FormulaExpression(ctx, generatorConf, storeMapNormal)
	}

	// 排序
	items = items.SortByGeneratorScore()
	// 只有第一页才打压
	if traceInfo.TraceRequest.PageNum == 1 || len(traceInfo.TraceRequest.NextPageToken) == 0 {
		items = DeboostReRankInGenFormula(ctx, traceInfo, items, adsOrIntentStoreNum, DeboostRelevance)
		items = DeboostReRankInGenFormula(ctx, traceInfo, items, adsOrIntentStoreNum, DeboostCategory)
	}
	//与广告、干预门店一起，截取seqLen 20个门店作为候选序列
	j := 0
	normalLen := len(items)
	for i := 0; i < seqLen; i++ {
		if storesSelect[i] == nil && j < normalLen {
			storesSelect[i] = storeMapNormal[items[j].StoreId]
			storesSelect[i].DeboostType = items[j].DeboostType
			items[j].StoreType = storesSelect[i].ItemFeature.GetCIStoreType()
			itemsSelect[i] = items[j]
			j++
		}
	}
	storeList := &model.StoreList{
		GeneratorConf:  generatorConf,
		ListWiseItems:  itemsSelect,
		StoresOriginal: storesSelect,
	}
	return storeList
}

func FormulaExpression(ctx context.Context, generatorConf apollo.ListWiseGeneratorConfig, storeMapNormal map[uint64]*model.StoreInfo) model.ListWiseItems {
	// 获取表达式
	expression := util.BuildExpression(ctx, generatorConf.Formula)

	// 计算分数，新增 generate score, 用于排序
	items := make(model.ListWiseItems, 0, len(storeMapNormal))
	for _, store := range storeMapNormal {
		// 公共权重参数填充，w1~w7,为了防止门店直接数据覆盖，不能挪到外层直接复用parameters，得新建
		parameters := util.BuildExpParameters(generatorConf.Parameters)
		store.BuildListWiseStoreParams(parameters)
		score, _ := util.EvaluateScore(ctx, expression, parameters)
		items = append(items, &model.ListWiseItem{
			StoreId:              store.StoreId,
			DisplayOpeningStatus: store.DisplayOpeningStatus,
			ExactMatch:           store.ExactMatch,
			PRelevanceScore:      store.PRelevanceScore,
			DeboostCategoryScore: store.DeboostCategoryScore,
			GeneratorScore:       score,
		})
	}
	return items
}

// w1*log(max(pctr,expN(2.7182818,-20)))+w2*log(max(pcvr,expN(2.7182818,-20)))+w3*log(max((relevance+1.0)/2.0,expN(2.7182818,-20)))+w4*log(max(dist_score,0.001))+w5*log(max(pue,expN(2.7182818,-20)))+w6*(relevance < w7 && relevance != 0.01 ? -1.0 : 0.0) + w8*(((relevance >= w9 && relevance <= w10) ? 1 : 0) + (relevance > w10 ? 1.2 : 0))
func FormulaStatic1(ctx context.Context, generatorConf apollo.ListWiseGeneratorConfig, storeMapNormal map[uint64]*model.StoreInfo) model.ListWiseItems {
	// 获取权重参数
	params := util.BuildExpParameters(generatorConf.Parameters)
	var w1, w2, w3, w4, w5, w6, w7, w8, w9, w10 = 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0
	if params["w1"] != nil {
		w1 = params["w1"].(float64)
	}
	if params["w2"] != nil {
		w2 = params["w2"].(float64)
	}
	if params["w3"] != nil {
		w3 = params["w3"].(float64)
	}
	if params["w4"] != nil {
		w4 = params["w4"].(float64)
	}
	if params["w5"] != nil {
		w5 = params["w5"].(float64)
	}
	if params["w6"] != nil {
		w6 = params["w6"].(float64)
	}
	if params["w7"] != nil {
		w7 = params["w7"].(float64)
	}
	if params["w8"] != nil {
		w8 = params["w8"].(float64)
	}
	if params["w9"] != nil {
		w9 = params["w9"].(float64)
	}
	if params["w10"] != nil {
		w10 = params["w10"].(float64)
	}

	// expN(2.7182818,-20) 计算
	epsilonValue := math.Pow(2.7182818, -20)

	// 计算分数，新增 generate score, 用于排序
	items := make(model.ListWiseItems, 0, len(storeMapNormal))
	for _, store := range storeMapNormal {
		// 获取各个特征值
		pctr := float64(store.ItemFeature.GetCPctr())
		pcvr := float64(store.ItemFeature.GetCPcvr())
		relevance := float64(store.ItemFeature.GetCPredictRelevanceScore())
		distScore := float64(store.ItemFeature.GetCStoreDistanceScore())
		pue := float64(store.ItemFeature.GetCPredictUeFactor())

		// 计算公式各部分
		term1 := w1 * math.Log(math.Max(pctr, epsilonValue))
		term2 := w2 * math.Log(math.Max(pcvr, epsilonValue))
		term3 := w3 * math.Log(math.Max((relevance+1.0)/2.0, epsilonValue))
		term4 := w4 * math.Log(math.Max(distScore, 0.001))
		term5 := w5 * math.Log(math.Max(pue, epsilonValue))

		// 条件判断部分
		var term6 float64
		if relevance < w7 && relevance != 0.01 {
			term6 = w6 * (-1.0)
		} else {
			term6 = w6 * 0.0
		}

		// 复杂条件判断部分
		var conditionPart1, conditionPart2 float64
		if relevance >= w9 && relevance <= w10 {
			conditionPart1 = 1.0
		} else {
			conditionPart1 = 0.0
		}
		if relevance > w10 {
			conditionPart2 = 1.2
		} else {
			conditionPart2 = 0.0
		}
		term7 := w8 * (conditionPart1 + conditionPart2)

		// 最终分数
		score := term1 + term2 + term3 + term4 + term5 + term6 + term7

		items = append(items, &model.ListWiseItem{
			StoreId:              store.StoreId,
			DisplayOpeningStatus: store.DisplayOpeningStatus,
			ExactMatch:           store.ExactMatch,
			PRelevanceScore:      store.PRelevanceScore,
			DeboostCategoryScore: store.DeboostCategoryScore,
			GeneratorScore:       score,
		})
	}
	return items
}

// w1*log(max(pctr,0.00000000206))+w2*log(max(pcvr,0.00000000206))+w3*log(max(relevance,0.00000000206))+w4*log(max(dist_score,0.001))+w5*log(max(pue,0.00000000206))
func FormulaStatic2(ctx context.Context, generatorConf apollo.ListWiseGeneratorConfig, storeMapNormal map[uint64]*model.StoreInfo) model.ListWiseItems {
	// 获取权重参数
	params := util.BuildExpParameters(generatorConf.Parameters)
	var w1, w2, w3, w4, w5 = 1.0, 1.0, 1.0, 1.0, 1.0
	if params["w1"] != nil {
		w1 = params["w1"].(float64)
	}
	if params["w2"] != nil {
		w2 = params["w2"].(float64)
	}
	if params["w3"] != nil {
		w3 = params["w3"].(float64)
	}
	if params["w4"] != nil {
		w4 = params["w4"].(float64)
	}
	if params["w5"] != nil {
		w5 = params["w5"].(float64)
	}
	// 计算分数，新增 generate score, 用于排序
	items := make(model.ListWiseItems, 0, len(storeMapNormal))
	for _, store := range storeMapNormal {
		score := w1*math.Log(math.Max(float64(store.ItemFeature.GetCPctr()), 0.00000000206)) +
			w2*math.Log(math.Max(float64(store.ItemFeature.GetCPcvr()), 0.00000000206)) +
			w3*math.Log(math.Max(float64(store.ItemFeature.GetCPredictRelevanceScore()), 0.00000000206)) +
			w4*math.Log(math.Max(float64(store.ItemFeature.GetCStoreDistanceScore()), 0.001)) +
			w5*math.Log(math.Max(float64(store.ItemFeature.GetCPredictUeFactor()), 0.00000000206))
		items = append(items, &model.ListWiseItem{
			StoreId:              store.StoreId,
			DisplayOpeningStatus: store.DisplayOpeningStatus,
			ExactMatch:           store.ExactMatch,
			PRelevanceScore:      store.PRelevanceScore,
			DeboostCategoryScore: store.DeboostCategoryScore,
			GeneratorScore:       score,
		})
	}
	return items
}

func GenMMRList(ctx context.Context, traceInfo *traceinfo.TraceInfo, candidateStores model.StoreInfos, seqLen int, mmrConf apollo.ListWiseGeneratorConfig) *model.StoreList {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseLWRankGenMMRList, time.Since(pt))
	}()
	// 选择非ads, inter 门店
	storeMapNormal := make(map[uint64]*model.StoreInfo, len(candidateStores)) // 普通门店，用于融合算分排序
	storesSelect := make(model.StoreInfos, seqLen)                            // 最后选中的门店
	itemsSelect := make([]*model.ListWiseItem, seqLen)
	for i, store := range candidateStores {
		// 1表示干预门店，值=2表示广告，值=3表示自然门店
		if store.ItemFeature.GetCIStoreType() == model.StoreTypeAds || store.ItemFeature.GetCIStoreType() == model.StoreTypeInter || store.ExactMatch > 0 {
			if i < seqLen {
				storesSelect[i] = store // 前 seqLen 的广告、干预门店，直接原位置保留
				itemsSelect[i] = &model.ListWiseItem{
					StoreId:   store.StoreId,
					StoreType: store.ItemFeature.GetCIStoreType(),
				}
			}
			continue // 超过seqLen 的广告、干预门店，跳过不参与
		}
		storeMapNormal[store.StoreId] = store
	}
	items := maximalMarginalRelevance(ctx, traceInfo, storeMapNormal, mmrConf, seqLen)
	//与广告、干预门店一起，截取seqLen 20个门店作为候选序列
	j := 0
	normalLen := len(items)
	for i := 0; i < seqLen; i++ {
		if storesSelect[i] == nil && j < normalLen {
			storesSelect[i] = storeMapNormal[items[j].StoreId]
			items[j].StoreType = storesSelect[i].ItemFeature.GetCIStoreType()
			itemsSelect[i] = items[j]
			j++
		}
	}
	storeList := &model.StoreList{
		GeneratorConf:  mmrConf,
		ListWiseItems:  itemsSelect,
		StoresOriginal: storesSelect,
	}
	return storeList
}

// 计算最大边缘相关性（MMR）
func maximalMarginalRelevance(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeMap map[uint64]*model.StoreInfo, mmrConf apollo.ListWiseGeneratorConfig, seqLen int) []*model.ListWiseItem {
	storeLen := len(storeMap)
	selectedStores := make([]*model.ListWiseItem, 0, storeLen)
	// 计算门店与查询的相关性
	remainingItems := calculateRelevance(ctx, traceInfo, storeMap, mmrConf)
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseLWRankGenMMRListDoMMR, time.Since(pt))
	}()
	lambdaFactor := 1 - mmrConf.Lambda
	isUseExp, w1, w2, simExpression := isMMRSimUseExpression(ctx, traceInfo, mmrConf)
	batchSize := traceInfo.AbParamClient.GetParamWithInt("Search.LWReRank.CalMMRBatch", 20)
	for len(remainingItems) > 0 {
		ptOne := time.Now()
		var wg sync.WaitGroup
		remainingLen := len(remainingItems)
		mmrScores := make([]float64, remainingLen)
		total := (remainingLen + batchSize - 1) / batchSize
		for i := 0; i < total; i++ {
			wg.Add(1)
			goroutine.WithGo(ctx, "CountMMRScores", func(params ...interface{}) {
				defer wg.Done()
				param := params[0].([]interface{})
				groupIndex := param[0].(int)
				begin := groupIndex * batchSize
				end := begin + batchSize
				if end > remainingLen {
					end = remainingLen
				}
				for index := begin; index < end; index++ {
					item := remainingItems[index]
					// 计算门店与已选的门店冗余度
					maxRedundancy := selectMaxSimBatch(ctx, traceInfo, selectedStores, item, storeMap, isUseExp, w1, w2, simExpression, mmrConf.SimParameters)
					// 计算 MMR 值
					mmrScore := item.MMRRelevanceLambda - lambdaFactor*maxRedundancy + item.MMROpeningStatusScore
					mmrScores[index] = mmrScore
				}
			}, i)
		}
		wg.Wait()
		var bestStore *model.ListWiseItem
		var bestScore = -math.MaxFloat64
		var bestStoreIndex int
		for i := range remainingItems {
			if mmrScores[i] > bestScore {
				bestScore = mmrScores[i]
				bestStore = remainingItems[i]
				bestStoreIndex = i
			}
		}
		// 将最佳门店添加到已选门店列表
		bestStore.MMRScore = bestScore
		selectedStores = append(selectedStores, bestStore)
		// 从剩余门店中移除已选门店
		remainingItems = append(remainingItems[:bestStoreIndex], remainingItems[bestStoreIndex+1:]...)
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseLWRankGenMMRListDoMMROne, time.Since(ptOne))
		// 选择门店有20个之后，直接退出计算
		if len(selectedStores) >= seqLen {
			break
		}
	}
	logkit.FromContext(ctx).Debug("maximalMarginalRelevance", logkit.Int("store len", len(storeMap)), logkit.String("cost", time.Since(pt).String()))
	return selectedStores
}

func isMMRSimUseExpression(ctx context.Context, traceInfo *traceinfo.TraceInfo, mmrConf apollo.ListWiseGeneratorConfig) (bool, float64, float64, *govaluate.EvaluableExpression) {
	isUseExp := false
	generatorNamesStr := traceInfo.AbParamClient.GetParamWithString("Search.LWReRank.MMRSimUseExpression", "") // mmr 想使用动态公式的generator name, 如"g1,g2"
	if len(generatorNamesStr) > 0 {
		generatorNames := strings.Split(generatorNamesStr, ",")
		for _, generatorName := range generatorNames {
			if generatorName == mmrConf.Name {
				isUseExp = true
				break
			}
		}
	}
	var w1, w2 float64
	var simExpression *govaluate.EvaluableExpression
	if isUseExp {
		simExpression = util.BuildExpression(ctx, mmrConf.SimFormula) // 动态表达式扩展性好，但是性能较差。
	} else {
		params := util.BuildExpParameters(mmrConf.SimParameters)
		if params["w1"] != nil {
			w1 = params["w1"].(float64)
		}
		if params["w2"] != nil {
			w2 = params["w2"].(float64)
		}
	}
	return isUseExp, w1, w2, simExpression
}

func selectMaxSimBatch(ctx context.Context, traceInfo *traceinfo.TraceInfo, selectedItems []*model.ListWiseItem, remainingItem *model.ListWiseItem, storeMap map[uint64]*model.StoreInfo, isUseExpression bool, w1, w2 float64, expression *govaluate.EvaluableExpression, paramStr string) float64 {
	if len(selectedItems) == 0 {
		return 0.0
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseLWRankGenMMRListCalSim, time.Since(pt))
	}()
	var wg sync.WaitGroup
	batchSize := traceInfo.AbParamClient.GetParamWithInt("Search.LWReRank.CalSimBatch", 5)
	itemLen := len(selectedItems)
	simScores := make([]float64, itemLen)
	total := (itemLen + batchSize - 1) / batchSize
	remainingId := remainingItem.StoreId
	for i := 0; i < total; i++ {
		wg.Add(1)
		goroutine.WithGo(ctx, "calculateSimilarity", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			groupIndex := param[0].(int)
			begin := groupIndex * batchSize
			end := begin + batchSize
			if end > itemLen {
				end = itemLen
			}
			for index := begin; index < end; index++ {
				// 计算已选门店与当前的门店相似度
				if isUseExpression && expression != nil {
					simScores[index] = calculateSimilarity(ctx, traceInfo, storeMap[selectedItems[index].StoreId], storeMap[remainingId], expression, paramStr)
				} else {
					simScores[index] = calculateSimilarityDirectly(ctx, traceInfo, storeMap[selectedItems[index].StoreId], storeMap[remainingId], w1, w2)
				}
			}
		}, i)
	}
	wg.Wait()
	maxRedundancy := -math.MaxFloat64
	for i := range selectedItems {
		if simScores[i] > maxRedundancy {
			maxRedundancy = simScores[i]
		}
	}
	return maxRedundancy
}

func calculateRelevance(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeMap map[uint64]*model.StoreInfo, mmrConf apollo.ListWiseGeneratorConfig) model.ListWiseItems {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseLWRankGenMMRListCalRel, time.Since(pt))
	}()
	remainingItems := make(model.ListWiseItems, 0, len(storeMap))
	relExpression := util.BuildExpression(ctx, mmrConf.RelFormula)
	for _, store := range storeMap {
		// 公共权重参数填充，w1~w7
		relParameters := util.BuildExpParameters(mmrConf.RelParameters)
		store.BuildListWiseStoreParams(relParameters)
		relevance, _ := util.EvaluateScore(ctx, relExpression, relParameters)
		remainingItems = append(remainingItems, &model.ListWiseItem{
			StoreId:               store.StoreId,
			DisplayOpeningStatus:  store.DisplayOpeningStatus,
			ExactMatch:            store.ExactMatch,
			MMRRelevance:          relevance,
			MMRRelevanceLambda:    mmrConf.Lambda * relevance,
			MMROpeningStatusScore: getMMROpeningStatusScore(store),
		})
	}
	return remainingItems
}

func getMMROpeningStatusScore(store *model.StoreInfo) float64 {
	switch store.DisplayOpeningStatus {
	case o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN:
		return 1000000.0
	case o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_PAUSE:
		return 10000.0
	case o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_CLOSE:
		return 0.0
	default:
		return 0.0
	}
}

// w1*log(max(l2_cate_sim,expN(2.7182818,-20)))+w2*log(max(jaccard_sim,expN(2.7182818,-20)))
// 当l2_cate_sim = 0时， log(max(l2_cate_sim,expN(2.7182818,-20))) = -20
// 当l2_cate_sim = 1时， log(max(l2_cate_sim,expN(2.7182818,-20))) = 0
func calculateSimilarityDirectly(ctx context.Context, traceInfo *traceinfo.TraceInfo, store1, store2 *model.StoreInfo, w1, w2 float64) float64 {
	l2CateSimLog := -20.0
	// store.main_category.l2_name  相等时 l2_cate_sim = 1， 否则 l2_cate_sim = 0，
	if store1.MainCategoryL2NameStr == store2.MainCategoryL2NameStr {
		l2CateSimLog = 0
	}
	// 计算相似度值
	jaccardSim := calJaccardSim(store1.StoreNameTerms, store2.StoreNameTerms)
	var sim float64
	if jaccardSim == 0 {
		sim = w1*l2CateSimLog + w2*-20.0
	} else {
		sim = w1*l2CateSimLog + w2*math.Log(math.Max(jaccardSim, epsilon))
	}
	return sim
}
func calculateSimilarity(ctx context.Context, traceInfo *traceinfo.TraceInfo, store1, store2 *model.StoreInfo, expression *govaluate.EvaluableExpression, paramStr string) float64 {
	// 计算相似度公式中的各项：log(max(...)) 处理以避免出现负数或零
	// 公共权重参数填充，w1~w7
	params := util.BuildExpParameters(paramStr)
	l2CateSim := 0
	// store.main_category.l2_name  相等时 l2_cate_sim = 1， 否则 l2_cate_sim = 0，
	if store1.MainCategoryL2NameStr == store2.MainCategoryL2NameStr {
		l2CateSim = 1
	}
	// 计算相似度值
	params["l2_cate_sim"] = l2CateSim
	params["jaccard_sim"] = calJaccardSim(store1.StoreNameTerms, store2.StoreNameTerms)
	sim, _ := util.EvaluateScore(ctx, expression, params)
	return sim
}

// #(token_list1 ∩ token_list2) / #(token_list1 U #token_list2)
func calJaccardSim(terms1, terms2 map[string]struct{}) float64 {
	if terms1 == nil || terms2 == nil {
		return 0
	}
	// 用于存储交集的个数
	intersection := 0
	unionCount := len(terms1) // 将 map1 中的每个键都加入并集
	// 遍历 map1 的键
	for key := range terms1 {
		// 如果 map2 中也有相同的键，交集计数加 1
		if _, exists := terms2[key]; exists {
			intersection++
		}
	}

	// 遍历 map2 的键，考虑 map2 中的那些键不在 map1 中的情况
	for key := range terms2 {
		// 如果该键还没有出现在 map1 中，则加入并集
		// 避免重复计算 map1 中已经遍历过的键
		if _, exists := terms1[key]; !exists {
			unionCount++
		}
	}
	if unionCount == 0 || intersection == 0 {
		return 0
	}
	return float64(intersection) / float64(unionCount)
}

func AddContextFeatureForListWise(ctx context.Context, traceInfo *traceinfo.TraceInfo, seqLen int) {
	if traceInfo.ContextFeature == nil {
		traceInfo.ContextFeature = &food.ContextFeature{}
	}
	traceInfo.ContextFeature.CActualSeqLen = int64(seqLen)
	data, _ := proto.Marshal(traceInfo.ContextFeature)
	traceInfo.ContextFeatureBytes = data
}
