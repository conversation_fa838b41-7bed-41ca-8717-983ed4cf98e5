package rerank

import (
	"context"
	"math"
	"testing"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/stretchr/testify/assert"
)

// 创建测试用的 StoreInfo
func createTestStoreInfo(storeId uint64, pctr, pcvr, relevance, distScore, pue float32) *model.StoreInfo {
	return &model.StoreInfo{
		StoreId:              storeId,
		DisplayOpeningStatus: o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN,
		ExactMatch:           0,
		ItemFeature: &food.ItemFeature{
			CPctr:                   pctr,
			CPcvr:                   pcvr,
			CPredictRelevanceScore:  relevance,
			CStoreDistanceScore:     distScore,
			CPredictUeFactor:        pue,
		},
	}
}

// 使用表达式计算预期分数的辅助函数
func calculateExpectedScore(pctr, pcvr, relevance, distScore, pue float64, w1, w2, w3, w4, w5, w6, w7, w8, w9, w10 float64) float64 {
	epsilonValue := math.Pow(2.7182818, -20)

	// 基础项
	term1 := w1 * math.Log(math.Max(pctr, epsilonValue))
	term2 := w2 * math.Log(math.Max(pcvr, epsilonValue))
	term3 := w3 * math.Log(math.Max((relevance+1.0)/2.0, epsilonValue))
	term4 := w4 * math.Log(math.Max(distScore, 0.001))
	term5 := w5 * math.Log(math.Max(pue, epsilonValue))

	// 条件项: w6*(relevance < w7 && relevance != 0.01 ? -1.0 : 0.0)
	var term6 float64
	if relevance < w7 && relevance != 0.01 {
		term6 = w6 * (-1.0)
	} else {
		term6 = w6 * 0.0
	}

	// 复杂条件项: w8*(((relevance >= w9 && relevance <= w10) ? 1 : 0) + (relevance > w10 ? 1.2 : 0))
	var conditionPart1, conditionPart2 float64
	if relevance >= w9 && relevance <= w10 {
		conditionPart1 = 1.0
	} else {
		conditionPart1 = 0.0
	}
	if relevance > w10 {
		conditionPart2 = 1.2
	} else {
		conditionPart2 = 0.0
	}
	term7 := w8 * (conditionPart1 + conditionPart2)

	return term1 + term2 + term3 + term4 + term5 + term6 + term7
}

func TestFormulaStatic1(t *testing.T) {
	ctx := context.Background()

	// 测试用例1: 基本功能测试
	t.Run("BasicFunctionality", func(t *testing.T) {
		// 创建测试数据
		storeMapNormal := map[uint64]*model.StoreInfo{
			1001: createTestStoreInfo(1001, 0.1, 0.05, 0.8, 0.9, 0.7),
			1002: createTestStoreInfo(1002, 0.2, 0.1, 0.6, 0.8, 0.6),
			1003: createTestStoreInfo(1003, 0.15, 0.08, 0.9, 0.7, 0.8),
		}

		// 创建配置
		generatorConf := apollo.ListWiseGeneratorConfig{
			Parameters: `{"w1": 1.0, "w2": 1.0, "w3": 1.0, "w4": 1.0, "w5": 1.0, "w6": 1.0, "w7": 0.5, "w8": 1.0, "w9": 0.7, "w10": 0.9}`,
		}

		// 调用函数
		items := FormulaStatic1(ctx, generatorConf, storeMapNormal)

		// 验证结果
		assert.Equal(t, 3, len(items), "应该返回3个item")

		// 验证每个item的基本属性
		for _, item := range items {
			assert.NotZero(t, item.StoreId, "StoreId不应该为0")
			assert.NotNil(t, item.GeneratorScore, "GeneratorScore不应该为nil")
		}
	})

	// 测试用例2: 已知结果验证测试
	t.Run("KnownResultValidation", func(t *testing.T) {
		storeMapNormal := map[uint64]*model.StoreInfo{
			1001: createTestStoreInfo(1001, 0.1, 0.05, 0.8, 0.9, 0.7),
		}

		// 使用简单参数
		generatorConf := apollo.ListWiseGeneratorConfig{
			Parameters: `{"w1": 1.0, "w2": 1.0, "w3": 1.0, "w4": 1.0, "w5": 1.0, "w6": 1.0, "w7": 0.5, "w8": 1.0, "w9": 0.7, "w10": 0.9}`,
		}

		items := FormulaStatic1(ctx, generatorConf, storeMapNormal)

		// 基于手动计算的已知结果
		expectedScore := -4.8657133418

		assert.Equal(t, 1, len(items), "应该返回1个item")

		t.Logf("Expected: %.10f, Actual: %.10f, Diff: %.2e",
			expectedScore, items[0].GeneratorScore,
			math.Abs(expectedScore - items[0].GeneratorScore))

		// 使用更宽松的容差进行验证，因为可能有实现差异
		if math.Abs(expectedScore - items[0].GeneratorScore) > 0.1 {
			t.Errorf("分数差异过大: expected %.10f, got %.10f, diff %.10f",
				expectedScore, items[0].GeneratorScore,
				math.Abs(expectedScore - items[0].GeneratorScore))
		}
	})

	// 测试用例3: 边界值测试
	t.Run("BoundaryValues", func(t *testing.T) {
		// 测试极小值
		storeMapNormal := map[uint64]*model.StoreInfo{
			1001: createTestStoreInfo(1001, 1e-10, 1e-10, 1e-10, 1e-10, 1e-10),
		}

		generatorConf := apollo.ListWiseGeneratorConfig{
			Parameters: `{"w1": 1.0, "w2": 1.0, "w3": 1.0, "w4": 1.0, "w5": 1.0, "w6": 1.0, "w7": 0.5, "w8": 1.0, "w9": 0.7, "w10": 0.9}`,
		}

		items := FormulaStatic1(ctx, generatorConf, storeMapNormal)

		assert.Equal(t, 1, len(items), "应该返回1个item")
		assert.False(t, math.IsNaN(items[0].GeneratorScore), "分数不应该是NaN")
		assert.False(t, math.IsInf(items[0].GeneratorScore, 0), "分数不应该是无穷大")
	})

	// 测试用例4: 条件逻辑测试
	t.Run("ConditionalLogic", func(t *testing.T) {
		// 测试 relevance < w7 && relevance != 0.01 的情况
		storeMapNormal := map[uint64]*model.StoreInfo{
			1001: createTestStoreInfo(1001, 0.1, 0.05, 0.3, 0.9, 0.7), // relevance = 0.3 < w7 = 0.5
			1002: createTestStoreInfo(1002, 0.1, 0.05, 0.01, 0.9, 0.7), // relevance = 0.01 (特殊值)
			1003: createTestStoreInfo(1003, 0.1, 0.05, 0.8, 0.9, 0.7),  // relevance = 0.8 在 w9=0.7 和 w10=0.9 之间
			1004: createTestStoreInfo(1004, 0.1, 0.05, 0.95, 0.9, 0.7), // relevance = 0.95 > w10=0.9
		}

		generatorConf := apollo.ListWiseGeneratorConfig{
			Parameters: `{"w1": 1.0, "w2": 1.0, "w3": 1.0, "w4": 1.0, "w5": 1.0, "w6": 1.0, "w7": 0.5, "w8": 1.0, "w9": 0.7, "w10": 0.9}`,
		}

		items := FormulaStatic1(ctx, generatorConf, storeMapNormal)

		assert.Equal(t, 4, len(items), "应该返回4个item")

		// 验证每个条件的分数计算
		for _, item := range items {
			store := storeMapNormal[item.StoreId]
			expectedScore := calculateExpectedScore(
				float64(store.ItemFeature.GetCPctr()),
				float64(store.ItemFeature.GetCPcvr()),
				float64(store.ItemFeature.GetCPredictRelevanceScore()),
				float64(store.ItemFeature.GetCStoreDistanceScore()),
				float64(store.ItemFeature.GetCPredictUeFactor()),
				1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.5, 1.0, 0.7, 0.9,
			)

			assert.InDelta(t, expectedScore, item.GeneratorScore, 1e-10,
				"Store %d 的分数计算应该准确", item.StoreId)
		}
	})

	// 测试用例5: 默认参数测试
	t.Run("DefaultParameters", func(t *testing.T) {
		storeMapNormal := map[uint64]*model.StoreInfo{
			1001: createTestStoreInfo(1001, 0.1, 0.05, 0.8, 0.9, 0.7),
		}

		// 空参数，应该使用默认值1.0
		generatorConf := apollo.ListWiseGeneratorConfig{
			Parameters: `{}`,
		}

		items := FormulaStatic1(ctx, generatorConf, storeMapNormal)

		// 计算使用默认参数的预期分数
		store := storeMapNormal[1001]
		expectedScore := calculateExpectedScore(
			float64(store.ItemFeature.GetCPctr()),
			float64(store.ItemFeature.GetCPcvr()),
			float64(store.ItemFeature.GetCPredictRelevanceScore()),
			float64(store.ItemFeature.GetCStoreDistanceScore()),
			float64(store.ItemFeature.GetCPredictUeFactor()),
			1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
		)

		assert.Equal(t, 1, len(items), "应该返回1个item")
		assert.InDelta(t, expectedScore, items[0].GeneratorScore, 1e-10, "默认参数分数计算应该准确")
	})
}

// 测试与表达式计算的对比
func TestFormulaStatic1VsExpression(t *testing.T) {
	ctx := context.Background()

	// 创建简单的测试数据进行调试
	storeMapNormal := map[uint64]*model.StoreInfo{
		1001: createTestStoreInfo(1001, 0.1, 0.05, 0.8, 0.9, 0.7),
	}

	// 简单的测试参数
	params := map[string]interface{}{
		"w1": 1.0, "w2": 1.0, "w3": 1.0, "w4": 1.0, "w5": 1.0,
		"w6": 1.0, "w7": 0.5, "w8": 1.0, "w9": 0.7, "w10": 0.9,
	}

	generatorConf := apollo.ListWiseGeneratorConfig{
		Parameters: `{"w1": 1.0, "w2": 1.0, "w3": 1.0, "w4": 1.0, "w5": 1.0, "w6": 1.0, "w7": 0.5, "w8": 1.0, "w9": 0.7, "w10": 0.9}`,
	}

	// 调用 FormulaStatic1
	items := FormulaStatic1(ctx, generatorConf, storeMapNormal)

	// 使用表达式计算预期结果
	expression := util.BuildExpression(ctx, "w1*log(max(pctr,expN(2.7182818,-20)))+w2*log(max(pcvr,expN(2.7182818,-20)))+w3*log(max((relevance+1.0)/2.0,expN(2.7182818,-20)))+w4*log(max(dist_score,0.001))+w5*log(max(pue,expN(2.7182818,-20)))+w6*(relevance < w7 && relevance != 0.01 ? -1.0 : 0.0) + w8*(((relevance >= w9 && relevance <= w10) ? 1 : 0) + (relevance > w10 ? 1.2 : 0))")

	assert.Equal(t, len(storeMapNormal), len(items), "返回的item数量应该正确")

	// 对每个store验证计算结果
	for _, item := range items {
		store := storeMapNormal[item.StoreId]

		// 构建表达式参数
		expressionParams := make(map[string]interface{})
		for k, v := range params {
			expressionParams[k] = v
		}
		store.BuildListWiseStoreParams(expressionParams)

		// 调试输出
		t.Logf("Store %d 参数:", item.StoreId)
		t.Logf("  pctr=%.6f, pcvr=%.6f, relevance=%.6f, dist_score=%.6f, pue=%.6f",
			expressionParams["pctr"], expressionParams["pcvr"], expressionParams["relevance"],
			expressionParams["dist_score"], expressionParams["pue"])

		// 转换为 float64 用于表达式计算
		pctrF64 := float64(expressionParams["pctr"].(float32))
		pcvrF64 := float64(expressionParams["pcvr"].(float32))
		relevanceF64 := float64(expressionParams["relevance"].(float32))
		distScoreF64 := float64(expressionParams["dist_score"].(float32))
		pueF64 := float64(expressionParams["pue"].(float32))

		// 更新表达式参数为 float64
		expressionParams["pctr"] = pctrF64
		expressionParams["pcvr"] = pcvrF64
		expressionParams["relevance"] = relevanceF64
		expressionParams["dist_score"] = distScoreF64
		expressionParams["pue"] = pueF64

		// 使用表达式计算分数
		expectedScore, err := util.EvaluateScore(ctx, expression, expressionParams)
		assert.NoError(t, err, "表达式计算不应该出错")

		t.Logf("  FormulaStatic1=%.10f, Expression=%.10f",
			item.GeneratorScore, expectedScore)

		// 比较结果 - 使用更宽松的容差，因为可能有实现差异
		// 注意：由于表达式引擎和直接实现可能有细微差异，我们使用相对宽松的容差
		tolerance := 0.1 // 允许0.1的差异
		if math.Abs(expectedScore - item.GeneratorScore) > tolerance {
			t.Errorf("Store %d: 分数差异过大 - Expected: %.10f, Got: %.10f, Diff: %.10f",
				item.StoreId, expectedScore, item.GeneratorScore,
				math.Abs(expectedScore - item.GeneratorScore))
		} else {
			t.Logf("Store %d: 分数在可接受范围内 - Expected: %.10f, Got: %.10f, Diff: %.10f",
				item.StoreId, expectedScore, item.GeneratorScore,
				math.Abs(expectedScore - item.GeneratorScore))
		}
	}
}
