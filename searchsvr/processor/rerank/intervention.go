package rerank

import (
	"context"
	"math"
	"math/rand"
	"sort"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func Intervention(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenIntervention, len(stores))
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseReRankIntervention, time.Since(pt))
	}()

	//记录干预前的position
	storeIndexMap := make(map[uint64]int, len(stores))
	for i := range stores {
		stores[i].InterBeforePos = int64(i)
		storeIndexMap[stores[i].StoreId] = i
	}
	normalStores := make(model.StoreInfos, 0, len(stores))
	adsPos := make([]uint64, 0, 3)
	for i := 0; i < len(stores); i++ {
		store := stores[i]
		if store.BusinessType != foodalgo_search.BusinessType_Ads {
			normalStores = append(normalStores, store)
		} else {
			adsPos = append(adsPos, uint64(i))
		}
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "Intervention", zap.Int("normal store len", len(normalStores)),
		zap.Int("store len", len(stores)), zap.Any("ads pos", adsPos))
	//limit 减少广告的坑位
	limit := GetRealLimitCount(traceInfo, adsPos)
	//判断是否有干预门店需要置顶
	var optStores model.StoreInfos
	normalStores, optStores = DoInterventionFlagAndSort(ctx, traceInfo, normalStores, limit, adsPos, storeIndexMap)
	logger.MyDebug(ctx, traceInfo.IsDebug, "Intervention after sort", zap.Int("normal store len", len(normalStores)),
		zap.Int("store len", len(stores)), zap.Int("opt len", len(optStores)), zap.Any("ads pos", adsPos))

	normalIdx, optIdx := 0, 0
	idx := 0
	for ; idx < len(stores); idx++ {
		// 广告的位置,跳过
		if stores[idx].BusinessType == foodalgo_search.BusinessType_Ads {
			continue
		}
		if IsPosInList(traceInfo.OptIntervention.SlotsPosition, uint64(idx)) && optIdx < len(optStores) {
			optStores[optIdx].IsInterForSort = 1
			stores[idx] = optStores[optIdx]
			optIdx++
		} else if normalIdx < len(normalStores) {
			stores[idx] = normalStores[normalIdx]
			normalIdx++
		}
	}
	if idx != len(stores) {
		logkit.FromContext(ctx).Error("Intervention error, normalStore list end but store list not end", zap.Int("normal store len", len(normalStores)),
			zap.Int("store list len", len(stores)), zap.Int("opt list len", len(optStores)))
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "fixed slots after", zap.Any("normal", normalStores), zap.Any("opt", optStores),
		zap.Any("after opt", stores))
	// 检查最后的列表是否有重复的 ID
	checkMap := make(map[uint64]bool, len(stores))
	retStores := make(model.StoreInfos, 0, len(stores))
	for _, store := range stores {
		ok := checkMap[store.StoreId]
		if !ok {
			retStores = append(retStores, store)
			checkMap[store.StoreId] = true
		} else {
			logkit.FromContext(ctx).Error("storeID repeat!!!!!")
		}
	}

	//记录干预后的position
	for i := range stores {
		stores[i].InterAfterPos = int64(i)
	}
	return stores
}

func DoInterventionFlagAndSort(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos, limit int,
	adsPosition []uint64, storeIndexMap map[uint64]int) (model.StoreInfos, model.StoreInfos) {

	// 标记打标
	if len(stores) == 0 || traceInfo.IsSkipIntervention || traceInfo.TraceRequest.GetSortType() != foodalgo_search.SearchRequest_Relevance || !traceInfo.OptIntervention.IsStoreInterventionRecall ||
		limit == 0 {
		return stores, nil
	}

	//limit := interventionTypeLimitMap[traceInfo.OptIntervention.InterventionType]
	activeInterRecallStore := make(model.StoreInfos, 0) // 干预召回有效门店:1.是2路干预召回的，且需要排除非Top1类型非Opening的门店。 2.Top1类型干预门店的同品牌门店
	nonInterRecallStore := make(model.StoreInfos, 0)    // 非干预召回门店
	for i, doc := range stores {
		if (doc.StoreInterventionRecall == 1 || doc.StoreInterventionWithMerchantIDRecall == 1) &&
			(traceInfo.OptIntervention.InterventionType == traceinfo.InterventionTypeTop1 || doc.DisplayOpeningStatus == o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN) {
			activeInterRecallStore = append(activeInterRecallStore, stores[i])
		} else if len(nonInterRecallStore) < limit { // 最多只需要limit个
			nonInterRecallStore = append(nonInterRecallStore, doc)
		}
	}
	if len(activeInterRecallStore) == 0 {
		return stores, nil
	}
	// 特殊产品需求：干预置顶门店,需先内部排序后再进行干预
	if traceInfo.OptIntervention.InterventionType == traceinfo.InterventionTypeTop1 {
		sort.Slice(activeInterRecallStore, func(i, j int) bool {
			return activeInterRecallStore.LessForStoreIntention(i, j)
		})
	}
	activeInterRecallStoreAfterSelect := SelectOptInterventionStore(traceInfo, activeInterRecallStore, limit)
	// 真正干预召回的有效门店
	for _, s := range activeInterRecallStoreAfterSelect {
		if s.ItemFeature == nil {
			s.ItemFeature = &food.ItemFeature{}
		}
		s.ItemFeature.CIStoreType = model.StoreTypeInter
	}
	if traceInfo.OptIntervention.IsRotateMerchants {
		activeInterRecallStoreAfterSelect = activeInterRecallStoreAfterSelect.SortByRelevance(ctx, traceInfo)
	}
	if traceInfo.OptIntervention.InterventionType == traceinfo.InterventionTypeFixedSlots {
		positionAfterFilterAds := make([]uint64, 0, len(traceInfo.OptIntervention.SlotsPosition))
		for _, val := range traceInfo.OptIntervention.SlotsPosition {
			if IsPosInList(adsPosition, val) {
				continue
			}
			positionAfterFilterAds = append(positionAfterFilterAds, val)
		}
		activeInterRecallStoreAfterCheck := make(model.StoreInfos, 0, len(activeInterRecallStoreAfterSelect))
		positionIdx := 0
		// 去掉原本靠前的门店
		for idx := 0; idx < len(activeInterRecallStoreAfterSelect); idx++ {
			oneStore := activeInterRecallStoreAfterSelect[idx]
			oldIndex, ok := storeIndexMap[oneStore.StoreId]
			if ok && positionIdx < len(positionAfterFilterAds) {
				if uint64(oldIndex) >= positionAfterFilterAds[positionIdx] {
					activeInterRecallStoreAfterCheck = append(activeInterRecallStoreAfterCheck, oneStore)
					positionIdx++
				}
			}
		}
		// 指定位置插入的,将需要插入的结果返回
		// 从原始的门店列表中,移除需要干预的门店
		newStores := make(model.StoreInfos, 0, len(stores))
		for _, s := range stores {
			ignoreFlag := false
			for _, slotStore := range activeInterRecallStoreAfterCheck {
				if s.StoreId == slotStore.StoreId {
					ignoreFlag = true
					break
				}
			}
			if ignoreFlag {
				continue
			}
			newStores = append(newStores, s)
		}

		newStores.SortByRelevance(ctx, traceInfo)
		return newStores, activeInterRecallStoreAfterCheck
	}
	// 干预队列门店数M各，每个类型干预排序有效个数N
	// 如果 M >= N: 直接将N个门店标记，剩余不打标，重新参与排序
	// 如果 M < N:
	// 		Top1 类型：将干预队列中M门店个打标，剩余不打标，重新参与排序
	// 		Top5-Top20: 将干预队列中M个打标, 然后剩余N-M个名额，需要把原始队列的门店按顺序打标补齐到N个。
	activeInterStoreForSort := activeInterRecallStoreAfterSelect // 干预排序有效门店
	interLen := len(activeInterRecallStoreAfterSelect)
	if interLen < limit {
		if traceInfo.OptIntervention.InterventionType != traceinfo.InterventionTypeTop1 && len(nonInterRecallStore) > 0 {
			last := int(math.Min(float64(len(nonInterRecallStore)), float64(limit-interLen)))
			activeInterStoreForSort = append(activeInterStoreForSort, nonInterRecallStore[0:last]...)
		}
	}
	for _, store := range activeInterStoreForSort {
		store.IsInterForSort = 1
	}
	stores.SortByRelevance(ctx, traceInfo)
	return stores, nil
}

func IsPosInList(list []uint64, pos uint64) bool {
	for _, l := range list {
		if l == pos {
			return true
		}
	}
	return false
}

func SelectOptInterventionStore(traceInfo *traceinfo.TraceInfo, activeInterRecallStore model.StoreInfos, limit int) model.StoreInfos {
	retStoreList := make(model.StoreInfos, 0, limit)

	if len(activeInterRecallStore) <= limit {
		retStoreList = activeInterRecallStore
	} else {
		if traceInfo.OptIntervention.IsRotateMerchants && !apollo.SearchApolloCfg.CloseOptRotateMerchants {
			// 随机挑选
			rand.Seed(time.Now().UnixNano())
			idxMap := make(map[int]bool)
			l := len(activeInterRecallStore)

			for i := 0; i < limit; i++ {
				for true {
					idx := rand.Intn(l)
					ok := idxMap[idx]
					if !ok {
						// 未使用的index
						idxMap[idx] = true
						if idx < l && idx >= 0 {
							retStoreList = append(retStoreList, activeInterRecallStore[idx])
						}
						break
					}
				}
			}
		} else {
			retStoreList = activeInterRecallStore[:limit]
		}
	}
	return retStoreList
}

func GetRealLimitCount(traceInfo *traceinfo.TraceInfo, adsPos []uint64) int {
	interventionType := traceInfo.OptIntervention.InterventionType
	maxLimit := GetInterventionLimitCount(traceInfo, interventionType)

	retLimit := maxLimit

	for _, pos := range adsPos {
		// 指定位置干预,广告位占了干预位,减少需要干预的门店数量
		if traceInfo.OptIntervention.InterventionType == traceinfo.InterventionTypeFixedSlots {
			if IsPosInList(traceInfo.OptIntervention.SlotsPosition, pos) {
				retLimit -= 1
			}
		} else {
			if int(pos) <= maxLimit {
				retLimit -= 1
			}
		}
	}
	return retLimit
}

func GetInterventionLimitCount(traceInfo *traceinfo.TraceInfo, interventionType int32) int {
	if interventionType == traceinfo.InterventionTypeFixedSlots {
		return len(traceInfo.OptIntervention.SlotsPosition)
	}
	return interventionTypeLimitMap[interventionType]
}

var interventionTypeLimitMap = map[int32]int{
	traceinfo.InterventionTypeTop1:  5,
	traceinfo.InterventionTypeTop5:  5,
	traceinfo.InterventionTypeTop10: 10,
	traceinfo.InterventionTypeTop20: 20,
}
