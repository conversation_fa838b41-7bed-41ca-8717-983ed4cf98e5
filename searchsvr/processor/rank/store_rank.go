package rank

import (
	"context"
	"encoding/json"
	"errors"
	"sync"
	"time"

	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filter"
	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/relevance"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filling"
)

// ID/TH/MY 精排
func FusionRankITM(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos) model.StoreInfos {
	if cid.IsVN() {
		return storeInfos
	}
	if len(storeInfos) == 0 {
		return storeInfos
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseFusionRank, time.Since(pt))
	}()
	storeInfos = CalculateAndRankByRelevance(ctx, traceInfo, storeInfos) // 模型预估，融合公式计算分数
	// todo: delete it
	if abtest.GetIsPrintStoreIds(traceInfo.AbParamClient) {
		logkit.FromContext(ctx).Info("ForDebug CalculateAndRankByRelevance after storeIds", logkit.Uint64s("storeIds", model.GetStoreIds(storeInfos)))
		for i, store := range storeInfos {
			if i < 200 {
				storeJson, _ := jsoniter.MarshalToString(store)
				logkit.FromContext(ctx).Info("ForDebug CalculateAndRankByRelevance after store", logkit.Int("index", i), logkit.Uint64("storeId", store.StoreId), logkit.String("store", storeJson))
			}
		}
	}

	storeInfos = StoreRank(ctx, traceInfo, storeInfos)
	// todo: delete it
	if abtest.GetIsPrintStoreIds(traceInfo.AbParamClient) {
		logkit.FromContext(ctx).Info("ForDebug StoreRank after storeIds", logkit.Uint64s("storeIds", model.GetStoreIds(storeInfos)))
	}

	storeInfos = filter.RecallPriorityFilter(ctx, traceInfo, storeInfos) // 优先级截断
	storeInfos = StoreReRankPriority(ctx, traceInfo, storeInfos)         //忽略优先级重新排序
	storeA30ReRankConfig := abtest.GetA30ReRankConfig(traceInfo.IsDebug, traceInfo.AbParamClient)
	if storeA30ReRankConfig != nil && storeA30ReRankConfig.DivBoost != 0 {
		storeInfos = StoreA30ReRank(ctx, traceInfo, storeA30ReRankConfig, storeInfos)
	}
	return storeInfos
}

// vn 精排，依赖广告门店
func FusionRankVN(ctx context.Context, traceInfo *traceinfo.TraceInfo, normalStores, adsStores model.StoreInfos) model.StoreInfos {
	if cid.IsVN() == false {
		return normalStores
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseFusionRank, time.Since(pt))
	}()
	normalStores = CalculateAndRankByRelevance(ctx, traceInfo, normalStores) // 模型预估，融合公式计算分数
	normalStores = filter.DuplicateBrandMergeVN(ctx, traceInfo, normalStores, adsStores)
	normalStores = StoreRank(ctx, traceInfo, normalStores)
	normalStores = filter.RecallPriorityFilter(ctx, traceInfo, normalStores) // 优先级截断
	normalStores = StoreReRankPriority(ctx, traceInfo, normalStores)         //忽略优先级重新排序
	return normalStores
}

func FusionRankFewResult(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	if cid.IsVN() {
		return FusionRankVN(ctx, traceInfo, stores, nil)
	} else {
		return FusionRankITM(ctx, traceInfo, stores)
	}
}

// 排序：融合排序
func StoreRank(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFusionRankSort, time.Since(pt))
	}()
	if len(stores) == 0 {
		return stores
	}
	switch traceInfo.TraceRequest.GetSortType() {
	case foodalgo_search.SearchRequest_Relevance:
		stores.SortByRelevance(ctx, traceInfo)
	case foodalgo_search.SearchRequest_Nearby:
		if traceInfo.IsNotRelevanceTabUseNewSort {
			stores.SortByNearbyV2()
		} else {
			stores.SortByNearby()
		}
	case foodalgo_search.SearchRequest_TopSales:
		if traceInfo.IsNotRelevanceTabUseNewSort {
			stores.SortByTopSalesV2()
		} else {
			stores.SortByTopSales()
		}
	case foodalgo_search.SearchRequest_BestRated:
		if traceInfo.IsNotRelevanceTabUseNewSort {
			stores.SortByRatingV2()
		} else {
			stores.SortByRating()
		}
	default:
		stores.SortByESScore()
	}
	return stores
}

// 排序：MainSite 融合排序
func MainSiteStoreRank(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	if len(stores) == 0 {
		return stores
	}

	// 2024Q4 重新打分
	reCalcMainSiteStoreScore(ctx, traceInfo, stores)

	return stores.SortMainSiteByRelevance(traceInfo.IsDebug)
}

func StoresAffiliateRank(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	if len(stores) <= 1 {
		return stores
	}
	switch traceInfo.TraceRequest.GetSortType() {
	case foodalgo_search.SearchRequest_Relevance:
		stores.SortByAffiliateRelevance()
	case foodalgo_search.SearchRequest_TopSales:
		stores.SortByAffiliateTopSales()
	default:
		stores.SortByAffiliateCommissionRate()
	}
	return stores
}

func StoreReRankPriority(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	if len(stores) == 0 {
		return stores
	}
	if traceInfo.SortPriorityConfig.IsUseSortPriority {
		if cid.IsVN() && traceInfo.PipelineType == traceinfo.PipelineTypeSearch && traceInfo.TraceRequest.GetSortType() == foodalgo_search.SearchRequest_Relevance {
			stores.SortByRelevance(ctx, traceInfo)
		}
		return stores
	}
	if cid.IsVN() && traceInfo.PipelineType == traceinfo.PipelineTypeSearch && traceInfo.TraceRequest.GetSortType() == foodalgo_search.SearchRequest_Relevance {
		if traceInfo.RecallPriorityConfig.IsReRankSkipPriority == 1 || traceInfo.RecallPriorityConfig.IsReRankSkipPriorityRecallLayer == 1 {
			traceInfo.IsReRankSkipPriorityFlag = true // 设置为true后，才会跳过优先级排序
			stores = model.SortByRelevanceVNWithoutPriority(ctx, stores, traceInfo)
		}
		return stores
	}
	return stores
}

// 模型预估，融合算分
func CalculateAndRankByRelevance(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	if len(stores) == 0 {
		return stores
	}
	if decision.IsNeedPredictAndReRankScore(traceInfo) == false {
		return stores
	}
	pt := time.Now()
	nStores := len(stores)
	if env.GetCID() != cid.VN {
		stores.CalculateParam(ctx, traceInfo)
	}
	traceInfo.CtrModelInfo = buildModelInfo(traceInfo, CtrPredictor)
	traceInfo.CvrModelInfo = buildModelInfo(traceInfo, CvrPredictor)
	traceInfo.UEModelInfo = buildModelInfo(traceInfo, UEPredictor)
	traceInfo.RelModelInfo = buildModelInfo(traceInfo, RelPredictor)
	traceInfo.DFModelInfo = buildModelInfo(traceInfo, DFPredictor)
	if traceInfo.CtrModelInfo.IsNeedPredict || traceInfo.CvrModelInfo.IsNeedPredict || traceInfo.RelModelInfo.IsNeedPredict || traceInfo.UEModelInfo.IsNeedPredict {
		_ = callPredictor(ctx, stores, traceInfo, nStores)
	}
	traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFusionRankPredict, time.Since(pt))

	pct := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFusionRankCalculate, time.Since(pct))
	}()

	if decision.IsUseOriginalReRank(traceInfo) {
		filling.RankingScoreFillingV1(ctx, traceInfo, stores)
	} else {
		filling.OfflineCtrCvrDataDefaultFilling(ctx, traceInfo, stores)
		filling.RelevanceDefaultFilling(ctx, traceInfo, stores)
		stores.CalculatePLtrV1Score(ctx, traceInfo)
		UpdatePredictFeatures(ctx, traceInfo, stores)
		stores.CalculateReRankScore(ctx, traceInfo, traceInfo.PredictConfig.UseLtr)
		if traceInfo.IsNotRelevanceTabUseNewSort {
			// 计算 非相关性 tab 页使用的新分数, sort type = 2,3,4才会使用
			stores.CalculateNotRelTabScore(ctx, traceInfo)
		}
	}
	return stores
}

// LTR 模型需要额外填充精排的特征
func UpdatePredictFeatures(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	isPRel := false
	if traceInfo.PredictConfig.NewStrategy == 4 && len(traceInfo.RelModelInfo.ModelName) > 0 {
		isPRel = true
	}
	for _, store := range stores {
		if store.ItemFeature == nil {
			store.ItemFeature = &food.ItemFeature{}
		}
		store.ItemFeature.CPctr = float32(store.PCtrScore)
		store.ItemFeature.CPcvr = float32(store.PCvrScore)
		store.ItemFeature.CPredictUeFactor = float32(store.PUEScore)
		if isPRel {
			store.ItemFeature.CPredictRelevanceScore = float32(store.PRelevanceScore) // 实验组使用相关性模型
		} else {
			store.ItemFeature.CPredictRelevanceScore = float32(store.RelevanceScore) // base组使用相关性服务
		}
		//logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:ItemFeature UpdatePredictFeatures", logkit.Any("store id", store.StoreId), logkit.Any("item", store.ItemFeature))
	}
}

func buildModelInfo(traceInfo *traceinfo.TraceInfo, predictor int) apollo.ModelInfo {
	modelInfo := apollo.ModelInfo{}
	if cid.IsVN() {
		isNeedPredict := false
		if traceInfo.PipelineType == traceinfo.PipelineTypeSearch && traceInfo.IsVnMart == false {
			isNeedPredict = true
		}
		if traceInfo.PipelineType == traceinfo.PipelineTypeSearchStoresForAffiliate {
			isNeedPredict = true
		}
		if traceInfo.PipelineType == traceinfo.PipelineTypeSearchFewResult {
			isNeedPredict = true
		}
		if isNeedPredict == false {
			return modelInfo
		}
	}

	modelInfo.IsNeedPredict = true
	switch predictor {
	case CtrPredictor:
		modelInfo.ModelName = traceInfo.PredictConfig.CtrModuleName
	case CvrPredictor:
		modelInfo.ModelName = traceInfo.PredictConfig.CvrModuleName
	case RelPredictor:
		modelInfo.ModelName = traceInfo.PredictConfig.RelevanceModuleName
	case UEPredictor:
		modelInfo.ModelName = traceInfo.PredictConfig.UEModuleName
	case DFPredictor:
		modelInfo.ModelName = traceInfo.PredictConfig.DFModelName
	case LtrPredictor:
		modelInfo.ModelName = traceInfo.PredictConfig.LtrModuleName
	}
	return modelInfo
}

func GetRelStores(traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	openingStores := make([]*model.StoreInfo, 0, len(stores))
	truncate := apollo.SearchApolloCfg.PredictRelTruncate
	if truncate != 0 && len(stores) > truncate {
		for _, store := range stores {
			if (traceInfo.OptIntervention.InterventionType == traceinfo.InterventionTypeTop1 && store.StoreInterventionRecall == 1) || store.DisplayOpeningStatus == o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN {
				openingStores = append(openingStores, store)
			}
		}
	} else {
		openingStores = stores
	}
	return openingStores
}

func callPredictor(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo, nStores int) AbstractRanker {
	levelDowngradeConfig := apollo.SearchApolloCfg.LevelDowngradeConfigMap.GetLevelDowngradeConfigControl(traceInfo.DowngradeLevel)
	if levelDowngradeConfig != nil && levelDowngradeConfig.DowngradePredict {
		traceInfo.IsSkipModel = true
		metric_reporter.ReportClientRequestError(1, "predict_all_downgrade", "true")
		traceInfo.SlaCode.UpdateErrorCode(sla_code.RANK_ERROR)
		return nil
	}
	if traceInfo.IsSkipModel {
		logkit.Debug("skip model")
		return nil
	}
	traceInfo.AddPhraseStoreLength(ctx, "RankCount", len(stores))
	metric_reporter.ReportClientRequestError(1, "predict_all_downgrade", "0")
	ctxFea := traceInfo.ContextFeatureBytes
	itemFea, itemIds := buildItemFeature(ctx, traceInfo, stores, nStores)
	predictors := make([]AbstractRanker, TotalPredictor, TotalPredictor)
	predictors[CtrPredictor] = NewCtrRanker()
	predictors[CtrPredictor].setFeatures(ctxFea, itemFea, itemIds)
	predictors[CvrPredictor] = NewCvrRanker()
	predictors[CvrPredictor].setFeatures(ctxFea, itemFea, itemIds)
	predictors[UEPredictor] = NewUERanker()
	predictors[UEPredictor].setFeatures(ctxFea, itemFea, itemIds)
	predictors[RelPredictor] = NewRelRanker()
	predictors[RelPredictor].setFeatures(ctxFea, itemFea, itemIds)
	predictors[DFPredictor] = NewDynamicFactor()
	predictors[DFPredictor].setFeatures(ctxFea, itemFea, itemIds)

	wg := &sync.WaitGroup{}

	for i := 0; i < TotalPredictor; i++ {
		wg.Add(1)
		goroutine.WithGo(ctx, "GetModelScore", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			index := param[0].(int)
			if predictors[index] == nil {
				return
			}
			err := predictors[index].doRank(ctx, stores, traceInfo, nStores)
			if err != nil {
				logkit.FromContext(ctx).WithError(err).Error("GetModelScore failed", logkit.String("predictorName", getPredictorName(index)))
				return
			}
		}, i)
	}
	wg.Wait()
	return predictors[CtrPredictor]
}

// ID调用老的相关性服务，后面会删除，改成调用相关性模型
func ProcessWithRelevanceServer(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	if decision.IsDowngrade(apollo.SearchApolloCfg.DowngradeServerConfig.DowngradeRelevance) {
		logger.MyDebug(ctx, traceInfo.IsDebug, "ProcessWithRelevanceServer downgrade relevance")
		reporter.ReportClientRequestError(1, "get_relevance_downgrade", "true")
		return
	}
	pt := time.Now()
	relevanceReq := relevance.PackRelevanceRequests(ctx, traceInfo, stores)
	// diff 流量不调用相关性服务
	if decision.IsMockModelScore(ctx, traceInfo) {
		mockDocItems := mockRelevanceScoreWhenDiff(relevanceReq)
		if mockDocItems != nil && len(mockDocItems) == len(relevanceReq.StoreItem) {
			for index, docItem := range mockDocItems {
				filling.RelevanceScoresFilling(traceInfo, stores[index], float64(docItem.GetLevel()), float64(docItem.GetScore()), float64(docItem.GetSemanticScore()))
			}
			for _, s := range stores {
				s.IsMockRelScore = 1
			}
		}
		traceInfo.RelModelInfo.IsPredictSuccess = true
		return
	}

	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFusionRankPredictRelevanceServer, time.Since(pt))
	}()
	reporter.ReportClientRequestError(1, "get_relevance_downgrade", "0")

	relevanceResp, err := relevance.GetRelevanceInfos(ctx, traceInfo, relevanceReq)
	if err != nil || relevanceResp == nil {
		traceInfo.AddErrorToTraceInfo(err)
		reqJson, _ := jsoniter.MarshalToString(relevanceReq)
		logkit.FromContext(ctx).WithError(err).Error("GetRelevanceInfos, error!", logkit.Any("req", reqJson))
		reporter.ReportClientRequestError(1, "get_relevance", "failed")
		return
	}
	reporter.ReportClientRequestError(1, "get_relevance", "0")
	docItems := relevanceResp.GetDocItem()
	if len(stores) != len(docItems) {
		reqJson, _ := jsoniter.MarshalToString(relevanceReq)
		rspJson, _ := jsoniter.MarshalToString(relevanceResp)
		traceInfo.AddErrorToTraceInfo(errors.New("predict old rel scoresLen diff"))
		logkit.FromContext(ctx).Error("get relevance failed, rel scoresLen diff", logkit.Any("req", reqJson), logkit.Any("rsp", rspJson))
		return
	}
	for index, docItem := range docItems {
		if traceInfo.IsDebug {
			logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:relevance server doc items", zap.Uint64("store id", docItem.GetStoreId()), zap.Any("docItem", docItem))
		}
		filling.RelevanceScoresFilling(traceInfo, stores[index], float64(docItem.GetLevel()), float64(docItem.GetScore()), float64(docItem.GetSemanticScore()))
	}
	traceInfo.RelModelInfo.IsPredictSuccess = true
}

// 算法需求，对主站门店重新打分
func reCalcMainSiteStoreScore(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model.StoreInfo) {
	if len(apollo.SearchApolloCfg.MainSiteStoreScoreExpression) == 0 {
		logkit.FromContext(ctx).Error("reCalcStoreScore failed without expression")
		return
	}
	if len(apollo.SearchApolloCfg.MainSiteStoreScoreWeightMap) == 0 {
		logkit.FromContext(ctx).Error("reCalcStoreScore failed without weightMap")
		return
	}

	//  is_dish_intention*w1*dish_text_match_score + is_store_intention*(w2*store_text_match_score + w3*category_match_score)
	// +w4* sales_volume_score  + w5*dist_score + w6*relevance_score
	expression := util.BuildExpression(ctx, apollo.SearchApolloCfg.MainSiteStoreScoreExpression)

	// 公共权重
	parameters := util.BuildExpParameters(apollo.SearchApolloCfg.MainSiteStoreScoreWeightMap)
	parametersSize := len(parameters)

	// 当不是门店意图、菜品意图的时候，都改为 0.5
	isStoreIntention := util.BoolToFloat(len(traceInfo.QPResult.QueryStoreIntention) > 0)
	isDishIntention := util.BoolToFloat(len(traceInfo.QPResult.QueryDishIntention) > 0)
	if isStoreIntention == 0.0 && isDishIntention == 0.0 {
		isStoreIntention = 0.5
		isDishIntention = 0.5
	}

	// 公共参数
	parameters["is_store_intention"] = isStoreIntention
	parameters["is_dish_intention"] = isDishIntention

	// 主站 dish score 重新打分
	for _, store := range stores {
		// 每次都要重新升成一个 params，防止数据污染
		currentParameters := make(map[string]interface{}, 2*parametersSize)
		for k, v := range parameters {
			currentParameters[k] = v
		}

		// 理论上来说这里 store 必须携带 dish，为了代码健壮性增加一些兜底处理
		var dishTextMatchScore float64
		if len(store.DishInfos) == 0 {
			logkit.FromContext(ctx).Error("reCalcStoreScore store should be with dishes", logkit.Uint64("storeId", store.StoreId))
			dishTextMatchScore = apollo.SearchApolloCfg.MainSiteDishTextMatchDefaultScore
		} else {
			dishTextMatchScore = store.DishInfos[0].DishTextMatchScore
		}

		currentParameters["dish_text_match_score"] = dishTextMatchScore
		currentParameters["store_text_match_score"] = store.StoreTextMatchScore
		currentParameters["category_match_score"] = store.StoreCategoryScore
		currentParameters["dist_score"] = store.DistanceScoreV2()
		currentParameters["sales_volume_score"] = store.GetSalesVolumeScore()

		if traceInfo.IsDebug {
			parameterStr, _ := json.Marshal(currentParameters)
			store.MainSiteScoreExpressionStr = string(parameterStr)
		}

		score, err := util.EvaluateScore(ctx, expression, currentParameters)
		if err == nil {
			store.MainSiteScore = score
		}
	}
}
