package rank

import (
	"context"
	"math"
	"testing"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/stretchr/testify/assert"
)

// 创建测试用的 StoreInfo
func createTestStoreInfo(storeId uint64, pctr, pcvr, relevance, distScore, ue, ueRadio float64) *model.StoreInfo {
	return &model.StoreInfo{
		StoreId:              storeId,
		DisplayOpeningStatus: o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN,
		PCtrScore:            pctr,
		PCvrScore:            pcvr,
		PRelevanceScore:      relevance,
		DistanceScore:        distScore,
		ItemFeature: &food.ItemFeature{
			CPctr:                  float32(pctr),
			CPcvr:                  float32(pcvr),
			CPredictRelevanceScore: float32(relevance),
			CStoreDistanceScore:    float32(distScore),
			CPredictUeFactor:       float32(ue),
		},
		StoreUE:      ue,
		StoreUERadio: ueRadio,
	}
}

// 模拟 SearchParamMultiClient
type mockSearchParamMultiClient struct {
	params map[string]string
}

func (m *mockSearchParamMultiClient) GetParamWithString(key, defaultValue string) string {
	if val, ok := m.params[key]; ok {
		return val
	}
	return defaultValue
}

func (m *mockSearchParamMultiClient) GetParamWithInt(key string, defaultValue int) int {
	return defaultValue
}

func (m *mockSearchParamMultiClient) GetParamWithInt64(key string, defaultValue int64) int64 {
	return defaultValue
}

func (m *mockSearchParamMultiClient) GetParamWithFloat(key string, defaultValue float64) float64 {
	return defaultValue
}

// 创建测试用的 TraceInfo
func createTestTraceInfo(ltrv1Params string) *traceinfo.TraceInfo {
	return &traceinfo.TraceInfo{
		AbParamClient: &abtest.SearchParamMultiClient{
			AbParamClient: &mockSearchParamMultiClient{
				params: map[string]string{
					"Search.MultiFactor.LTRV1ExpParameters": ltrv1Params,
					"Search.MultiFactor.LTRV1ExpString":     "1/(1.0+expN(2.7182818,-1.0*(dw1*log(max(pctr,expN(2.7182818,-20)))+dw2*log(max(pcvr,expN(2.7182818,-20)))+dw3*log(max(p_relevance,expN(2.7182818,-20)))+dw4*log(max(dist_score,0.001))+dw5*log(max(ue,expN(2.7182818,-5)))+dw6*log(max(ue_radio,expN(2.7182818,-20))))))",
				},
			},
		},
		PredictConfig: &traceinfo.PredictConfig{
			UEDefaultVal:      0.5,
			UERadioDefaultVal: 0.6,
		},
	}
}

func TestCalculatePLtrV1Score_Consistency(t *testing.T) {
	ctx := context.Background()

	// 测试用例1: 基本一致性测试
	t.Run("BasicConsistency", func(t *testing.T) {
		// 创建测试数据
		stores := model.StoreInfos{
			createTestStoreInfo(1001, 0.1, 0.05, 0.8, 0.9, 0.7, 0.6),
			createTestStoreInfo(1002, 0.2, 0.1, 0.6, 0.8, 0.6, 0.5),
			createTestStoreInfo(1003, 0.15, 0.08, 0.9, 0.7, 0.8, 0.7),
		}

		// 使用默认参数
		traceInfo := createTestTraceInfo("dw1=0.983,dw2=0.8135,dw3=0.0342,dw4=0.0572,dw5=0,dw6=0.9943")

		// 复制数据用于两个函数
		stores1 := make(model.StoreInfos, len(stores))
		stores2 := make(model.StoreInfos, len(stores))
		for i, store := range stores {
			// 深拷贝
			stores1[i] = &model.StoreInfo{}
			*stores1[i] = *store
			stores1[i].ItemFeature = &food.ItemFeature{}
			*stores1[i].ItemFeature = *store.ItemFeature

			stores2[i] = &model.StoreInfo{}
			*stores2[i] = *store
			stores2[i].ItemFeature = &food.ItemFeature{}
			*stores2[i].ItemFeature = *store.ItemFeature
		}

		// 调用两个函数
		result1 := stores1.CalculatePLtrV1Score(ctx, traceInfo)
		CalculatePLtrV1ScoreStatic(ctx, traceInfo, stores2)

		// 验证结果一致性
		assert.Equal(t, len(result1), len(stores2), "返回的store数量应该一致")

		for i := 0; i < len(result1); i++ {
			store1 := result1[i]
			store2 := stores2[i]

			assert.Equal(t, store1.StoreId, store2.StoreId, "StoreId应该一致")

			// 验证 PLtrV1Score 的一致性
			assert.InDelta(t, store1.PLtrV1Score, store2.PLtrV1Score, 1e-10,
				"Store %d: PLtrV1Score应该一致 - CalculatePLtrV1Score: %.10f, CalculatePLtrV1ScoreStatic: %.10f",
				store1.StoreId, store1.PLtrV1Score, store2.PLtrV1Score)

			t.Logf("Store %d: CalculatePLtrV1Score=%.10f, CalculatePLtrV1ScoreStatic=%.10f, Diff=%.2e",
				store1.StoreId, store1.PLtrV1Score, store2.PLtrV1Score,
				math.Abs(store1.PLtrV1Score-store2.PLtrV1Score))
		}
	})

	// 测试用例2: 不同参数的一致性测试
	t.Run("DifferentParametersConsistency", func(t *testing.T) {
		// 创建测试数据
		stores := model.StoreInfos{
			createTestStoreInfo(2001, 0.05, 0.02, 0.7, 0.95, 0.65, 0.55),
			createTestStoreInfo(2002, 0.25, 0.15, 0.5, 0.75, 0.55, 0.45),
		}

		// 使用自定义参数
		traceInfo := createTestTraceInfo("dw1=1.2,dw2=0.9,dw3=0.05,dw4=0.08,dw5=0.1,dw6=1.1")

		// 复制数据
		stores1 := make(model.StoreInfos, len(stores))
		stores2 := make(model.StoreInfos, len(stores))
		for i, store := range stores {
			stores1[i] = &model.StoreInfo{}
			*stores1[i] = *store
			stores1[i].ItemFeature = &food.ItemFeature{}
			*stores1[i].ItemFeature = *store.ItemFeature

			stores2[i] = &model.StoreInfo{}
			*stores2[i] = *store
			stores2[i].ItemFeature = &food.ItemFeature{}
			*stores2[i].ItemFeature = *store.ItemFeature
		}

		// 调用两个函数
		result1 := stores1.CalculatePLtrV1Score(ctx, traceInfo)
		CalculatePLtrV1ScoreStatic(ctx, traceInfo, stores2)

		// 验证结果一致性
		for i := 0; i < len(result1); i++ {
			store1 := result1[i]
			store2 := stores2[i]

			assert.InDelta(t, store1.PLtrV1Score, store2.PLtrV1Score, 1e-10,
				"Store %d: 自定义参数下PLtrV1Score应该一致", store1.StoreId)
		}
	})

	// 测试用例3: 边界值测试
	t.Run("BoundaryValues", func(t *testing.T) {
		// 创建边界值测试数据
		stores := model.StoreInfos{
			createTestStoreInfo(3001, 1e-10, 1e-10, 1e-10, 1e-10, 1e-10, 1e-10), // 极小值
			createTestStoreInfo(3002, 0.99, 0.95, 0.99, 0.999, 0.95, 0.98),      // 接近1的值
			createTestStoreInfo(3003, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5),             // 中等值
		}

		traceInfo := createTestTraceInfo("dw1=0.983,dw2=0.8135,dw3=0.0342,dw4=0.0572,dw5=0,dw6=0.9943")

		// 复制数据
		stores1 := make(model.StoreInfos, len(stores))
		stores2 := make(model.StoreInfos, len(stores))
		for i, store := range stores {
			stores1[i] = &model.StoreInfo{}
			*stores1[i] = *store
			stores1[i].ItemFeature = &food.ItemFeature{}
			*stores1[i].ItemFeature = *store.ItemFeature

			stores2[i] = &model.StoreInfo{}
			*stores2[i] = *store
			stores2[i].ItemFeature = &food.ItemFeature{}
			*stores2[i].ItemFeature = *store.ItemFeature
		}

		// 调用两个函数
		result1 := stores1.CalculatePLtrV1Score(ctx, traceInfo)
		CalculatePLtrV1ScoreStatic(ctx, traceInfo, stores2)

		// 验证结果一致性和有效性
		for i := 0; i < len(result1); i++ {
			store1 := result1[i]
			store2 := stores2[i]

			// 验证分数在合理范围内 (0, 1)
			assert.True(t, store1.PLtrV1Score > 0 && store1.PLtrV1Score < 1,
				"Store %d: CalculatePLtrV1Score结果应该在(0,1)范围内: %.10f", store1.StoreId, store1.PLtrV1Score)
			assert.True(t, store2.PLtrV1Score > 0 && store2.PLtrV1Score < 1,
				"Store %d: CalculatePLtrV1ScoreStatic结果应该在(0,1)范围内: %.10f", store2.StoreId, store2.PLtrV1Score)

			// 验证一致性
			assert.InDelta(t, store1.PLtrV1Score, store2.PLtrV1Score, 1e-10,
				"Store %d: 边界值下PLtrV1Score应该一致", store1.StoreId)

			// 验证不是NaN或无穷大
			assert.False(t, math.IsNaN(store1.PLtrV1Score), "CalculatePLtrV1Score结果不应该是NaN")
			assert.False(t, math.IsNaN(store2.PLtrV1Score), "CalculatePLtrV1ScoreStatic结果不应该是NaN")
			assert.False(t, math.IsInf(store1.PLtrV1Score, 0), "CalculatePLtrV1Score结果不应该是无穷大")
			assert.False(t, math.IsInf(store2.PLtrV1Score, 0), "CalculatePLtrV1ScoreStatic结果不应该是无穷大")
		}
	})

	// 测试用例4: 空参数测试（使用默认值）
	t.Run("EmptyParameters", func(t *testing.T) {
		stores := model.StoreInfos{
			createTestStoreInfo(4001, 0.1, 0.05, 0.8, 0.9, 0.7, 0.6),
		}

		// 空参数，应该使用默认值
		traceInfo := createTestTraceInfo("")

		// 复制数据
		stores1 := make(model.StoreInfos, len(stores))
		stores2 := make(model.StoreInfos, len(stores))
		for i, store := range stores {
			stores1[i] = &model.StoreInfo{}
			*stores1[i] = *store
			stores1[i].ItemFeature = &food.ItemFeature{}
			*stores1[i].ItemFeature = *store.ItemFeature

			stores2[i] = &model.StoreInfo{}
			*stores2[i] = *store
			stores2[i].ItemFeature = &food.ItemFeature{}
			*stores2[i].ItemFeature = *store.ItemFeature
		}

		// 调用两个函数
		result1 := stores1.CalculatePLtrV1Score(ctx, traceInfo)
		CalculatePLtrV1ScoreStatic(ctx, traceInfo, stores2)

		// 验证结果一致性
		store1 := result1[0]
		store2 := stores2[0]

		assert.InDelta(t, store1.PLtrV1Score, store2.PLtrV1Score, 1e-10,
			"空参数下PLtrV1Score应该一致")

		t.Logf("空参数测试: CalculatePLtrV1Score=%.10f, CalculatePLtrV1ScoreStatic=%.10f",
			store1.PLtrV1Score, store2.PLtrV1Score)
	})
}
