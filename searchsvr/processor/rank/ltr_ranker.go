package rank

import (
	"context"
	"encoding/json"
	"errors"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"math"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	predictor "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/predictor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

type LTRRanker struct {
	contextFea []byte
	itemFea    [][]byte
	itemIds    []uint64
}

func NewLTRRanker() *LTRRanker {
	ltr := &LTRRanker{}
	return ltr
}

func (ltr *LTRRanker) setFeatures(ctxFea []byte, itemFea [][]byte, itemIds []uint64) {
	ltr.contextFea = ctxFea
	ltr.itemFea = itemFea
	ltr.itemIds = itemIds
}

func (ltr *LTRRanker) doPredict(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo, nStores int, modelName string) error {
	req := &predictor.PredictReq{
		Uid: traceInfo.UserId,
		//Itemids: ltr.itemIds,
		CtxFea: ltr.contextFea,
		Reqid:  traceInfo.TraceRequest.PublishId,
		Models: []string{modelName},
	}
	if ltr.itemIds != nil {
		req.Itemids = ltr.itemIds
	}
	if ltr.itemFea != nil {
		req.ItemFeas = ltr.itemFea
	}

	// 检查是否为diff流量
	if decision.IsMockModelScore(ctx, traceInfo) {
		mockScores := mockPredictScoreWhenDiff(ctx, req, "ltr", true)
		if mockScores != nil && len(mockScores) == nStores {
			for i, s := range stores {
				s.PLtrScore = mockScores[i]
				s.IsMockPLtrScore = 1
			}
		}
		traceInfo.LtrModelInfo.IsPredictSuccess = true
		traceInfo.PredictConfig.LtrModuleName = modelName
		return nil
	}

	startTime := time.Now()
	errCode := "failed"
	name := "predict-ltr-" + modelName
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseReRankLTRPredict, time.Since(startTime))
		metric_reporter.ReportClientRequestError(1, name, errCode)
		if errCode == "failed" {
			traceInfo.SlaCode.UpdateErrorCode(sla_code.RERANK_ERROR)
		}
	}()
	resp, _, err := mlplatform.Predict(ctx, req, traceInfo.IsDebug)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", modelName), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", name)
	if err != nil {
		logkit.FromContext(ctx).Error("failed to predict dynamic factor", logkit.String("cost", time.Since(startTime).String()), logkit.Any("err", err), logkit.Any("module_name", traceInfo.PredictConfig.CvrModuleName))
		return err
	}
	if resp == nil || resp.GetErrcode() != 0 {
		logkit.FromContext(ctx).Error("failed to predict dynamic factor", logkit.Any("errCode", resp.GetErrcode()), logkit.Any("errReason", resp.GetErrdesc()))
		return errors.New(resp.GetErrdesc())
	}
	if traceInfo.IsDebug {
		reqStr, _ := json.Marshal(req)
		rspStr, _ := json.Marshal(resp)
		logkit.FromContext(ctx).Info("doPredict", logkit.String("modelName", modelName), logkit.String("req", string(reqStr)), logkit.String("rsp", string(rspStr)))
	}

	errCode = "0"
	scoreInfo := resp.GetModScoresInfo()[modelName]
	var scores *predictor.ScoreList
	if scoreInfo != nil {
		scores = scoreInfo.Scores["ltr"]
		if scores != nil && scores.Scores != nil {
			if len(scores.Scores) != nStores {
				metric_reporter.ReportClientRequestError(1, name, "len")
				logkit.FromContext(ctx).Error("failed to get ltr score", logkit.Int("scoresLen", len(scores.Scores)), logkit.Int("storesLen", nStores))
				return errors.New("predict ltr scoresLen diff")
			}
			for i := 0; i < nStores; i++ {
				stores[i].PLtrScore = scores.Scores[i]
			}
		}
	}
	if len(resp.GetModelInfo()) > 0 {
		traceInfo.LtrModelInfo.RspModelInfo = resp.ModelInfo[0]
	}
	return nil
}
func (ltr *LTRRanker) doRank(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo, nStores int) error {
	if decision.IsDowngrade(apollo.SearchApolloCfg.DowngradeServerConfig.DowngradePredictLtr) {
		logger.MyDebug(ctx, traceInfo.IsDebug, "ranker doRank downgrade DowngradePredictLtr")
		metric_reporter.ReportClientRequestError(1, "predict_ltr_downgrade", "true")
		return nil
	}
	metric_reporter.ReportClientRequestError(1, "predict_ltr_downgrade", "0")
	if traceInfo.PredictConfig.UseLtr && len(traceInfo.LtrModelInfo.ModelName) > 0 {
		err := ltr.doPredict(ctx, stores, traceInfo, nStores, traceInfo.LtrModelInfo.ModelName)
		if err != nil {
			traceInfo.AddErrorToTraceInfo(err)
			// Ltr模型失败,使用 pctr * pcvr 分数进行兜底
			for _, store := range stores {
				store.PLtrScore = store.PCtrScore * store.PCvrScore
			}
		}
	}
	return nil
}

// LTR 模型依赖pctr/pcvr 作为特征，需要在ctr预估预估之后.
// LTR 模型依赖relevance 分数，需要RelevanceDefaultFilling之后执行,且在CalculateReRankScore之前
func CalculateAfterLtrAndRankByRelevance(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	if len(stores) == 0 {
		return stores
	}
	if decision.IsNeedPredictAndReRankScore(traceInfo) == false {
		return stores
	}
	nStores := len(stores)
	if env.GetCID() != cid.VN {
		stores.CalculateParam(ctx, traceInfo)
	}
	traceInfo.LtrModelInfo = buildModelInfo(traceInfo, LtrPredictor)

	itemFeas, itemIds := buildItemFeatureForLtr(ctx, traceInfo, stores, nStores)
	ltrPredict := NewLTRRanker()
	ltrPredict.setFeatures(traceInfo.ContextFeatureBytes, itemFeas, itemIds)
	ltrPredict.doRank(ctx, stores, traceInfo, nStores)
	pct := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseReRankLTRCalculate, time.Since(pct))
	}()

	stores.CalculateReRankScore(ctx, traceInfo, false)

	return stores
}

// 原地计算
func CalculatePLtrV1Score(ctx context.Context, traceInfo *traceinfo.TraceInfo, s model.StoreInfos) {
	expString := abtest.GetLTRV1ExpString(traceInfo.AbParamClient)
	if len(expString) == 0 {
		return
	}

	expression := util.BuildExpression(ctx, expString)
	paramStr := abtest.GetLTRV1ExpParameters(traceInfo.AbParamClient)
	// 公式计算
	for _, store := range s {
		// 复用parameters，可能会有些值被污染，所以不放到外层创建
		parameters := util.BuildExpParameters(paramStr) // 公共权重参数填充，例如w1~w7
		// 填充因子
		store.BuildStoreParams(traceInfo.PredictConfig, parameters)
		score, _ := util.EvaluateScore(ctx, expression, parameters)
		store.PLtrV1Score = score
	}
}

// 原地计算
func CalculatePLtrV1ScoreStatic(ctx context.Context, traceInfo *traceinfo.TraceInfo, s model.StoreInfos) {
	// 静态公式实现: 1/(1.0+expN(2.7182818,-1.0*(dw1*log(max(pctr,expN(2.7182818,-20)))+dw2*log(max(pcvr,expN(2.7182818,-20)))+dw3*log(max(p_relevance,expN(2.7182818,-20)))+dw4*log(max(dist_score,0.001))+dw5*log(max(ue,expN(2.7182818,-5)))+dw6*log(max(ue_radio,expN(2.7182818,-20))))))
	// 默认参数: dw1=0.983,dw2=0.8135,dw3=0.0342,dw4=0.0572,dw5=0,dw6=0.9943

	paramStr := abtest.GetLTRV1ExpParameters(traceInfo.AbParamClient)
	params := util.BuildExpParameters(paramStr)

	// 解析参数，使用默认值作为后备
	var dw1, dw2, dw3, dw4, dw5, dw6 = 1.0, 1.0, 1.0, 1.0, 1.0, 1.0
	if params["dw1"] != nil {
		dw1 = params["dw1"].(float64)
	}
	if params["dw2"] != nil {
		dw2 = params["dw2"].(float64)
	}
	if params["dw3"] != nil {
		dw3 = params["dw3"].(float64)
	}
	if params["dw4"] != nil {
		dw4 = params["dw4"].(float64)
	}
	if params["dw5"] != nil {
		dw5 = params["dw5"].(float64)
	}
	if params["dw6"] != nil {
		dw6 = params["dw6"].(float64)
	}

	// epsilon 值
	epsilon20 := math.Pow(2.7182818, -20) // expN(2.7182818,-20)
	epsilon5 := math.Pow(2.7182818, -5)   // expN(2.7182818,-5)

	// 公式计算
	for _, store := range s {
		// 获取各个特征值
		pctr := store.PCtrScore
		pcvr := store.PCvrScore
		pRelevance := store.PRelevanceScore
		distScore := store.DistanceScore
		ue := store.GetStoreUE(traceInfo.PredictConfig.UEDefaultVal)
		ueRadio := store.GetStoreUERadio(traceInfo.PredictConfig.UERadioDefaultVal)

		// 计算公式各部分
		term1 := dw1 * math.Log(math.Max(pctr, epsilon20))
		term2 := dw2 * math.Log(math.Max(pcvr, epsilon20))
		term3 := dw3 * math.Log(math.Max(pRelevance, epsilon20))
		term4 := dw4 * math.Log(math.Max(distScore, 0.001))
		term5 := dw5 * math.Log(math.Max(ue, epsilon5))
		term6 := dw6 * math.Log(math.Max(ueRadio, epsilon20))

		// 计算内部表达式: dw1*log(...) + dw2*log(...) + ... + dw6*log(...)
		innerExpression := term1 + term2 + term3 + term4 + term5 + term6

		// 计算最终分数: 1/(1.0+expN(2.7182818, -1.0 * innerExpression))
		// expN(2.7182818, -1.0 * innerExpression) = math.Pow(2.7182818, -1.0 * innerExpression)
		denominator := 1.0 + math.Pow(2.7182818, -1.0*innerExpression)
		score := 1.0 / denominator

		store.PLtrV1Score = score
	}
	return
}
