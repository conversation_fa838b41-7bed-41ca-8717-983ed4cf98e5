package recall

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	featurepb "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/feature-server"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"runtime/debug"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall/parse"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"

	reporter2 "git.garena.com/shopee/feed/comm_lib/reporter"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/util"
)

func recallStoreFromFS(ctx context.Context, traceInfo *traceinfo.TraceInfo) ([]*model.RecallStore, error) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseStoreRecallMultiSourceFS, time.Since(pt))
	}()
	recallConfig := parse.GetStoreRecallConfigByPipelineType(ctx, traceInfo, recallconstant.RecallDomainStore, recallconstant.RecallDataSourceFS)
	if recallConfig == nil || len(recallConfig.StoreRecalls) == 0 {
		if traceInfo.IsDebug {
			logkit.FromContext(ctx).Info("recallStoreFromFS recall config is nil")
		}
		return nil, nil
	}

	recallConfig.StoreRecalls = parse.DoFilterCondition(ctx, traceInfo, recallConfig.StoreRecalls, recallconstant.RecallDomainStore)
	if len(recallConfig.StoreRecalls) == 0 {
		if traceInfo.IsDebug {
			logkit.FromContext(ctx).Info("recallStoreFromFS recall config is nil after filter conditions")
		}
		return nil, nil
	}

	if traceInfo.IsDebug {
		for _, x := range recallConfig.StoreRecalls {
			traceInfo.AppendRecallsStoreFinal(fmt.Sprintf("%s_%s", x.RecallName, x.RecallId))
		}
	}

	parallel := len(recallConfig.StoreRecalls)
	if parallel == 0 {
		return []*model.RecallStore{}, nil
	}
	errs := make(chan error, parallel)
	recallList := make([]*model.RecallStore, parallel)

	wg := &sync.WaitGroup{}
	wg.Add(parallel)
	for i, req := range recallConfig.StoreRecalls {
		go func(index int, recallConfig *apollo.StoreRecallConfig) {
			startTime := time.Now()
			defer wg.Done()
			defer func() {
				traceInfo.AddPhraseCostTime(ctx, traceinfo.CostPhrase(fmt.Sprintf("StoreFSRecall_%s_%s", recallConfig.RecallTypeStr, recallConfig.RecallId)), time.Since(startTime))
				if e := recover(); e != nil {
					logkit.Error("search fs panic", logkit.String("recall type", recallConfig.RecallTypeStr), logkit.Any("err", e), logkit.String("stack", util.ByteToString(debug.Stack())))
					reporter.ReportCounter(reporter.ReportServicePanicMetricName, 1, reporter2.Label{
						Key: "method",
						Val: "search-fs-store-" + recallConfig.RecallTypeStr,
					})
				}
			}()

			featureReq := &featurepb.ReadReq{}
			if recallConfig.FsRecallConfig.InputKeyType == "query_geohash5" {
				var key string
				if len(recallConfig.FsRecallConfig.DataVersion) > 0 {
					key = fmt.Sprintf("%s_%s_%s", traceInfo.QueryKeyword, traceInfo.UserContext.GeoHash5, recallConfig.FsRecallConfig.DataVersion)
				} else {
					key = fmt.Sprintf("%s_%s", traceInfo.QueryKeyword, traceInfo.UserContext.GeoHash5)
				}
				featureReq.StrKeys = []string{key}
			} else if recallConfig.FsRecallConfig.InputKeyType == "user_id" {
				featureReq.IntKeys = []uint64{traceInfo.UserId}
			} else {
				err := errors.New("recallStoreFromFS invalid InputKeyType" + recallConfig.FsRecallConfig.InputKeyType)
				logkit.FromContext(ctx).WithError(err).Error("recallStoreFromFS error invalid InputKeyType", logkit.String("recallName", recallConfig.RecallName), logkit.String("InputKeyType", recallConfig.FsRecallConfig.InputKeyType))
				traceInfo.SlaCode.UpdateErrorCode(sla_code.OTHER_RECALL_ERROR)
				errs <- err
				return
			}

			featureReq.Tags = append(featureReq.Tags, recallConfig.FsRecallConfig.Tags...)
			featureReq.FeatureGroup = recallConfig.FsRecallConfig.FeatureGroup
			featureRsp, err := mlplatform.ReadFeatureFromFeatureServer(ctx, featureReq)
			if err != nil {
				logkit.FromContext(ctx).WithError(err).Error("recallStoreFromFS error", logkit.String("recallName", recallConfig.RecallName))
				traceInfo.SlaCode.UpdateErrorCode(sla_code.OTHER_RECALL_ERROR)
				errs <- err
				return
			}
			if featureRsp == nil || len(featureRsp.Feas) == 0 {
				logkit.FromContext(ctx).Error("recallStoreFromFS featureRsp is nil", logkit.String("recallName", recallConfig.RecallName))
				traceInfo.SlaCode.UpdateErrorCode(sla_code.OTHER_RECALL_ERROR)
				errs <- errors.New("recallStoreFromFS featureRsp is nil")
				return
			}

			// 如果设置了过滤项目：距离，相关性分数，需要同步埋点
			var filterByRelevanceScore float64
			var filterByDistance float64
			if recallConfig.FilterByRelevanceScoreSwitch {
				filterByRelevanceScore = recallConfig.FilterByRelevanceScore
			}
			if recallConfig.FilterByDistanceSwitch {
				filterByDistance = recallConfig.FilterByDistance
			}

			storeIds := make([]uint64, 0)
			storeScores := make([]float64, 0)
			for _, feaList := range featureRsp.Feas {
				if feaList != nil && len(feaList.Features) > 0 {
					for _, fea := range feaList.Features {
						if fea == nil {
							continue
						}
						// 默认：int64是门店id
						for _, value := range fea.GetInt64Value() {
							storeIds = append(storeIds, uint64(value))
						}
						// 默认：float64是门店分数
						for _, value := range fea.GetFloatValue() {
							storeScores = append(storeScores, float64(value))
						}
					}
				}
			}
			stores := make(model.StoreInfos, 0)
			for j, storeId := range storeIds {
				// recallSize截断
				if j >= int(recallConfig.RecallSize) {
					break
				}
				store := &model.StoreInfo{
					StoreId:                           storeId,
					IsNeedDishRecall:                  true,
					RecallPriority:                    recallConfig.RecallPriority,
					RecallTypes:                       []string{recallConfig.RecallTypeStr},
					RecallTypeAndIds:                  []string{fmt.Sprintf("%s_%s", recallConfig.RecallTypeStr, recallConfig.RecallId)},
					RecallPosList:                     []string{fmt.Sprintf("%s_%d", recallConfig.RecallTypeStr, j)},
					FilterRecallConfigPRelevanceScore: filterByRelevanceScore,
					FilterRecallConfigDistance:        filterByDistance,
				}
				// 如果有分数的话
				if len(storeScores) > 0 && j < len(storeScores) {
					store.Score = storeScores[j]
					store.ESScore = storeScores[j]
					store.RecallScores = []float64{storeScores[j]}
				}
				stores = append(stores, store)
			}

			recallStore := &model.RecallStore{
				Stores:           stores,
				TotalHits:        uint64(len(stores)),
				RecallTypeStr:    recallConfig.RecallTypeStr,
				RecallId:         recallConfig.RecallId,
				IsNeedDishRecall: true,
			}
			recallList[index] = recallStore
		}(i, req)
	}
	wg.Wait()
	if len(errs) == parallel {
		logkit.FromContext(ctx).Error("recallStoreFromFS all failed")
		return nil, <-errs
	}
	return recallList, nil
}
