package polygon

import (
	"context"
	"strconv"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_datamanagement"
)

const mapFenceTagId = 1835011

func BuildPolygon(ctx context.Context, rsp *o2oalgo_datamanagement.GetDataRsp) (map[string]*Polygon, error) {
	// 构建多边形
	rawPolygonMap := map[string]string{}
	result := map[string]*Polygon{}
	for polygonKey, item := range rsp.Items {
		if item == nil {
			result[polygonKey] = nil
			continue
		}
		// 拿到门店ID
		for tagId, featureData := range item.Data {
			if tagId == mapFenceTagId {
				// store_id_list
				if featureData.GetBytesList() == nil || featureData.GetBytesList().GetValue() == nil {
					logkit.FromContext(ctx).Info("feature_data.GetBytesList() == nil, 1835011")
					continue
				}
				if len(featureData.GetBytesList().GetValue()) > 0 {
					rawPolygonMap[polygonKey] = string(featureData.GetBytesList().GetValue()[0])
					break
				}
			}
		}
	}
	if len(rawPolygonMap) < 1 {
		return result, nil
	}
	for storeId, polygonDataRaw := range rawPolygonMap {
		polygonData := trimPolygonData(polygonDataRaw)
		polygonVector := strings.Split(polygonData, ",")
		if len(polygonVector) >= 3 {
			pg := &Polygon{}
			for _, data := range polygonVector {
				if len(data) > 0 {
					data = strings.TrimSpace(data)
					dataVec := strings.Split(data, " ")
					if len(dataVec) == 2 {
						latitude, err1 := strconv.ParseFloat(dataVec[0], 64)  // latitude
						longitude, err2 := strconv.ParseFloat(dataVec[1], 64) // longitude
						if err1 == nil && err2 == nil {
							pg.Add(latitude, longitude)
						} else {
							logkit.FromContext(ctx).Error("polygon data, strconv failed", logkit.Any("err1", err1), logkit.Any("err2", err2))
						}
					} else {
						logkit.FromContext(ctx).Error("polygon data is not 2", logkit.Any("data", data))
					}
				}
			}
			result[storeId] = pg
		}
	}
	return result, nil
}

func trimPolygonData(raw string) string {
	if len(raw) < 1 {
		return raw
	}
	start := len(raw)
	for i := 0; i < len(raw); i++ {
		ch := raw[i]
		if ch >= '0' && ch <= '9' {
			start = i
			break
		}
	}
	raw = raw[start:]
	if len(raw) < 1 {
		return raw
	}
	end := 0
	for i := len(raw) - 1; i >= 0; i-- {
		ch := raw[i]
		if ch >= '0' && ch <= '9' {
			end = i + 1
			break
		}
	}
	raw = raw[:end]
	return raw
}
