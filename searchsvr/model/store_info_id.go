package model

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"math"
	"sort"
	"strconv"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"github.com/Knetic/govaluate"

	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_datamanagement"
	"github.com/micro/go-micro/metadata"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/pcfactor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func (s *StoreInfo) status() int32 {
	switch s.DisplayOpeningStatus {
	case o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN:
		return 0
	case o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_PAUSE:
		return 1
	case o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_CLOSE:
		return 2
	default:
		return math.MaxInt32
	}
}

// todo 待优化：1. suppress & IsStoreIncubation 是否可以删除， 2. ReRankScore 应该在外层赋值 4. predictConfig 应该去掉
func (s *StoreInfo) GetReRankScore(ctx context.Context, expression *govaluate.EvaluableExpression, predictConfig *traceinfo.PredictConfig, parameters map[string]interface{}, isSegmentTopN bool) float64 {
	if predictConfig == nil || expression == nil {
		s.ReRankScore = s.GetDefaultReRankScore()
	} else {
		s.BuildStoreParams(predictConfig, parameters)
		score, err := util.EvaluateScore(ctx, expression, parameters)
		if err != nil {
			score = s.GetDefaultReRankScore()
		}
		if s.RelevanceLevel == 0 && env.GetCID() != cid.VN {
			score = score * predictConfig.Suppress
		}
		if predictConfig.StoreIncubation != 0 && StoreIncubationDictInstance.IsStoreIncubation(s.StoreId) {
			score = score / predictConfig.StoreIncubation
		}
		s.ReRankScore = score
		s.CalculateReRankScoreWithoutSegmentBoost(ctx, isSegmentTopN, expression, parameters) // segment boost 独立计算
	}
	return s.ReRankScore
}

// 独立计算精排分数，所有的store segment boost factor == 1
func (s *StoreInfo) CalculateReRankScoreWithoutSegmentBoost(ctx context.Context, isSegmentTopN bool, expression *govaluate.EvaluableExpression, parameters map[string]interface{}) {
	if isSegmentTopN == false {
		return
	}
	segmentBoostFactor := parameters["segment_boost_factor"]
	parameters["segment_boost_factor"] = 1.0
	score, err := util.EvaluateScore(ctx, expression, parameters)
	if err != nil {
		score = s.GetDefaultReRankScore()
	}
	s.ReRankScoreWithoutSegmentBoost = score
	parameters["segment_boost_factor"] = segmentBoostFactor
}

// todo 后续去掉PredictConfig, 以及所有的参数直接放到store info
func (s *StoreInfo) BuildStoreParams(predictConfig *traceinfo.PredictConfig, parameters map[string]interface{}) {
	parameters["pctr"] = s.PCtrScore
	parameters["pcvr"] = s.PCvrScore
	parameters["ctr"] = s.CtrScoreOffline
	parameters["cvr"] = s.CvrScoreOffline
	parameters["relevance"] = s.GetRelevanceScore(predictConfig.Relevance)
	parameters["price"] = s.GetPriceScore(predictConfig.PriceType)
	parameters["pue"] = s.PUEScore
	parameters["ue"] = s.GetStoreUE(predictConfig.UEDefaultVal)
	parameters["ue_radio"] = s.GetStoreUERadio(predictConfig.UERadioDefaultVal)
	parameters["semantic_relevance"] = s.SemanticRelevanceScore
	parameters["dist_score"] = s.DistanceScore
	parameters["recent_sales"] = s.StoreSellScore
	parameters["rating_scores"] = s.RatingScoreNorm
	//parameters["ue_ration"] = s.UeRation
	parameters["revenue"] = s.Revenue
	parameters["commission"] = s.Commission
	parameters["opening_score"] = s.OpeningScore
	parameters["opening_ratio"] = s.OpeningRatio
	parameters["is_recall_suppress"] = s.IsRecallSuppress
	parameters["pltr"] = s.PLtrScore
	parameters["pltr_v1"] = s.PLtrV1Score
	parameters["p_relevance"] = s.PRelevanceScore
	parameters["segment_boost_factor"] = s.StoreSegmentBoostFactor
	parameters["normalized_score"] = s.NormalizedScore

	if len(predictConfig.DyFactorList) == 6 {
		parameters["dw1"] = predictConfig.DyFactorList[0]
		parameters["dw2"] = predictConfig.DyFactorList[1]
		parameters["dw3"] = predictConfig.DyFactorList[2]
		parameters["dw4"] = predictConfig.DyFactorList[3]
		parameters["dw5"] = predictConfig.DyFactorList[4]
		parameters["dw6"] = predictConfig.DyFactorList[5]
	}
}

func (s *StoreInfo) BuildListWiseStoreParams(parameters map[string]interface{}) {
	parameters["pctr"] = s.ItemFeature.GetCPctr()
	parameters["pcvr"] = s.ItemFeature.GetCPcvr()
	parameters["pue"] = s.ItemFeature.GetCPredictUeFactor()
	parameters["dist_score"] = s.ItemFeature.GetCStoreDistanceScore()
	parameters["sales_score"] = s.ItemFeature.GetCStoreSalesScore()
	parameters["acc_sales_score_cate1"] = s.ItemFeature.GetCAccSalesScoreCate1()
	parameters["acc_gmv_score_cate1"] = s.ItemFeature.GetCAccGmvScoreCate1()
	parameters["relevance"] = s.ItemFeature.GetCPredictRelevanceScore()
	parameters["price"] = s.ItemFeature.GetCPriceIndex()
	parameters["rating_scores"] = s.ItemFeature.GetCRatingScore()
	parameters["cate_relevance"] = s.ItemFeature.GetCICateRel()
	parameters["pltr_v1"] = s.PLtrV1Score
}

func (s *StoreInfo) GetDefaultReRankScore() float64 {
	// 当门店rating score 为0时，需要转为默认的4.0
	pctr := math.Min(s.PCtrScore, 0.5)
	ctr := math.Min(s.CtrScoreOffline, 0.5)
	cvr := math.Min(s.CvrScoreOffline, 0.4)
	pcvr := math.Min(s.PCvrScore, 0.4)
	if env.GetCID() == cid.TH || env.GetCID() == cid.MY {
		ctr = math.Min(s.CtrScoreOffline, 0.3)
		cvr = math.Min(s.CvrScoreOffline, 0.3)
	}
	if pctr == 0 {
		pctr = ctr
	}
	if pcvr == 0 {
		pcvr = cvr
	}
	relevance := s.GetRelevanceScore(1)
	score := 0.0
	switch env.GetCID() {
	case cid.ID:
		score = math.Pow(1+pctr*3, 3) * math.Pow(1+2*cvr, 3) * math.Pow(1+1*relevance, 1) * math.Pow(1+2*s.DistanceScore, 2)
		break
	case cid.TH:
		score = math.Pow(1+ctr*5, 2.85) * math.Pow(1+4.03*cvr, 2) * math.Pow(1+2*relevance, 6) * math.Pow(1+6*s.DistanceScore, 6) * math.Pow(1+s.StoreSellScore, 2) * math.Pow(1+0.5*s.RatingScoreNorm, 1)
		break
	case cid.MY:
		score = math.Pow(1+ctr*6.09, 4.51) * math.Pow(1+6.0*cvr, 2.13) * math.Pow(1+relevance, 3) * math.Pow(1+6*s.DistanceScore, 6) * math.Pow(1+s.StoreSellScore, 2) * math.Pow(1+0.5*s.RatingScoreNorm, 1)
		break
	case "xx":
		score = math.Pow(1+pctr*3, 3) * math.Pow(1+2*cvr, 3) * math.Pow(1+1*relevance, 1) * math.Pow(1+2*s.DistanceScore, 2)
		break
	case cid.VN:
		score = 8.42*math.Log(pctr+0.0000000001) + 10*math.Log(pcvr+0.0000000001) + math.Log(s.DistanceScore+0.0000000001) + 1.86*math.Log(s.RelevanceScore+0.0000000001)
	}
	s.ReRankScore = score
	return score
}

func (s *StoreInfo) GetRelevanceScore(relevance int) float64 {
	if env.GetCID() == cid.TH || env.GetCID() == cid.MY {
		return s.ESScore
	}

	if env.GetCID() == cid.VN {
		return s.RelevanceScore
	}
	score := int(math.Floor(s.Score))
	switch relevance {
	case 0:
		if score&bit7 == 0 && score&bit8 == 0 && score&bit9 == 0 && score&bit10 == 0 {
			return 0
		} else {
			return 1
		}
	case 1:
		if score&bit7 == 0 && score&bit8 == 0 && score&bit9 == 0 && score&bit10 == 0 {
			return 0
		} else if score&bit9 == 0 && score&bit10 == 0 && (score&bit7 != 0 || score&bit8 != 0) {
			return 0.25
		} else if score&bit9 == 0 && score&bit10 != 0 {
			return 0.5
		} else if score&bit9 != 0 {
			return 1
		}
	case 2:
		// 相关性模型分数
		return s.RelevanceScore
	default:
		break
	}
	return 0
}
func (s *StoreInfo) GetPriceScore(priceFuc int) float64 {
	switch priceFuc {
	case 1:
		return s.PriceScore1
	case 2:
		return s.PriceScore2
	case 3:
		return s.PriceScore3
	default:
		return s.PriceScore1
	}
}
func (s *StoreInfo) GetStoreUE(defaultVal float64) float64 {
	if defaultVal == 0 {
		if s.UE == 0 {
			return 0.3
		}
	}
	if s.UE == -1 {
		return defaultVal
	}
	return s.UE
}
func (s *StoreInfo) GetStoreUERadio(defaultVal float64) float64 {
	if defaultVal == 0 {
		if s.UERadio == 0 {
			return 0.16
		}
	}
	if s.UERadio == -1 {
		return defaultVal
	}
	return s.UERadio
}

func (s StoreInfos) Sort() StoreInfos {
	sort.Sort(s)
	return s
}

func DistanceLimit(distanceI, distanceJ float64) bool {
	return (distanceI <= DistanceLimit4km && distanceJ <= DistanceLimit4km) ||
		(distanceI > DistanceLimit4km && distanceI <= DistanceLimit10km && distanceJ > DistanceLimit4km && distanceJ <= DistanceLimit10km) ||
		(distanceI > DistanceLimit10km && distanceJ > DistanceLimit10km)
}

func (s StoreInfos) Len() int {
	return len(s)
}

func (s StoreInfos) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

// 1级排序：门店状态（open > pause > closed）
// 2级排序：店名匹配（完全匹配规则 > 非完全匹配规则）
func (s StoreInfos) Less(i, j int) bool {
	//if s[i].IsSortBySourceScore {
	//	return s[i].Score > s[j].Score
	//}
	statusI := s[i].status()
	statusJ := s[j].status()
	if statusI != statusJ {
		// 状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
		return statusI < statusJ
	}
	scoreI := s[i].ReRankScore
	scoreJ := s[j].ReRankScore
	distanceI := s[i].Distance
	distanceJ := s[j].Distance
	storeRatingScoreI := s[i].RatingScore
	storeRatingScoreJ := s[j].RatingScore
	StoreSellWeekI := s[i].StoreSellWeek
	StoreSellWeekJ := s[j].StoreSellWeek
	relevanceLevelI := s[i].RelevanceLevelForSort
	relevanceLevelJ := s[j].RelevanceLevelForSort
	if relevanceLevelI != relevanceLevelJ {
		// 一档的门店优先级更高
		return relevanceLevelI > relevanceLevelJ
	}
	if !acc.Equal(scoreI, scoreJ) {
		// 分数按照降序排序
		return scoreI > scoreJ
	}

	if !acc.Equal(distanceI, distanceJ) {
		// 分数相同，按距离由近到远排序（升序）
		return distanceI < distanceJ
	}
	if !acc.Equal(storeRatingScoreI, storeRatingScoreJ) {
		// 距离相同，按照店铺分降序
		return storeRatingScoreI > storeRatingScoreJ
	}
	if StoreSellWeekI != StoreSellWeekJ {
		// 店铺分相同，按照门店销量降序
		return StoreSellWeekI > StoreSellWeekJ
	}

	return s[i].StoreId < s[j].StoreId

}

// DistinctStoreIds 返回去重后的 StoreId 列表
func (s StoreInfos) DistinctStoreIds() []uint64 {
	idSet := make(map[uint64]struct{}, len(s))
	ids := make([]uint64, 0, len(s))
	for _, store := range s {
		if store == nil {
			continue
		}
		if _, ok := idSet[store.StoreId]; ok {
			continue
		}
		idSet[store.StoreId] = struct{}{}
		ids = append(ids, store.StoreId)
	}
	return ids
}

func (s StoreInfos) StoreIds() []uint64 {
	ids := make([]uint64, 0, len(s))
	for _, store := range s {
		if store == nil {
			continue
		}
		ids = append(ids, store.StoreId)
	}
	return ids
}
func (s StoreInfos) OpeningStores() StoreInfos {
	openingIDs := make(StoreInfos, 0, len(s))
	for _, store := range s {
		if store == nil ||
			store.DisplayOpeningStatus != o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN ||
			store.StoreId == 0 {
			continue
		}

		openingIDs = append(openingIDs, store)
	}
	return openingIDs
}

func (s StoreInfos) FilterInactiveDistrict(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeExts map[uint64]*o2oalgo_datamanagement.StoreMeta) StoreInfos {
	startTime := time.Now()
	StoreInfos := make([]*StoreInfo, 0, len(s))
	defer func() {
		metaData, _ := metadata.FromContext(ctx)
		metaData["filter_inactive_adr"] = strconv.Itoa(len(StoreInfos))
		metaData["filter_inactive_cost_ms"] = strconv.Itoa(int(time.Since(startTime)) / 1e6)
	}()
	filterInactiveDistrictIDs := make([]uint64, 0, len(s))
	for _, ss := range s {
		storeExt, ok := storeExts[ss.StoreId]
		if !ok || storeExt == nil {
			continue
		}

		if storeExt.GetStore().GetDisplayDistrictStatus() == o2oalgo.DisplayDistrictStatus_DISPLAY_DISTRICT_STATUS_INACTIVE {
			continue
		}
		StoreInfos = append(StoreInfos, ss)
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "SearchService.FilterInactiveDistrict",
		zap.Int("length", len(filterInactiveDistrictIDs)),
		zap.Any("filterInactiveDistrictIDs", filterInactiveDistrictIDs))
	return StoreInfos
}

// 完全匹配排序规则：
// 分数按照降序排序
// 分数相同，按距离由近到远排序（升序）
func (s StoreInfos) LessForStoreIntention(i int, j int) bool {
	statusI := s[i].status()
	statusJ := s[j].status()
	distanceI := s[i].Distance
	distanceJ := s[j].Distance

	if statusI != statusJ {
		// 分数相同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
		return statusI < statusJ
	}

	if !acc.Equal(distanceI, distanceJ) {
		// 状态相同，按距离由近到远排序（升序）
		return distanceI < distanceJ
	}

	// 距离相同，则 ID 升序
	return s[i].StoreId < s[j].StoreId
}

func (s StoreInfos) CalculateParam(ctx context.Context, traceInfo *traceinfo.TraceInfo) StoreInfos {
	var maxStoreSellWeek uint64
	var minStoreSellWeek uint64
	minStoreSellWeek = math.MaxUint32
	scoreDenominator := MaxScoreDenominator
	for _, v := range s {
		if v.StoreSellWeek > maxStoreSellWeek {
			maxStoreSellWeek = v.StoreSellWeek
		}
		if v.StoreSellWeek < minStoreSellWeek {
			minStoreSellWeek = v.StoreSellWeek
		}
	}
	storeSellDenominator := float64(maxStoreSellWeek - minStoreSellWeek)
	logger.MyDebug(ctx, traceInfo.IsDebug, "store sell score", zap.Uint64("max", maxStoreSellWeek), zap.Uint64("mix", minStoreSellWeek), zap.Float64("denominator", storeSellDenominator))
	for k, _ := range s {
		// 归一化门店销量
		if storeSellDenominator == 0 {
			s[k].StoreSellScore = float64(1)
		} else {
			s[k].StoreSellScore = float64(s[k].StoreSellWeek-minStoreSellWeek) / storeSellDenominator
		}
		s[k].Score = s[k].Score / float64(scoreDenominator)
		if env.GetCID() == cid.MY || env.GetCID() == cid.TH {
			RatingScoreSmooth := 15.0
			RatingScoreNorm := 30.0
			if env.GetCID() == cid.MY {
				RatingScoreSmooth = 21
				RatingScoreNorm = 45
			}
			// 当门店rating score 为0时，需要转为默认的4.0
			if s[k].RatingTotal == 0 {
				s[k].RatingScoreNorm = 4.0
			} else {
				minScore := math.Min(math.Log(float64(s[k].RatingTotal)+RatingScoreSmooth)/math.Log(RatingScoreNorm), 1.0)
				s[k].RatingScoreNorm = minScore * math.Max(2.5, s[k].RatingScore)
			}
		}
		// 提前赋默认值, 防止predict或relevance调用失败,使用的排序分数都为0
		s[k].ReRankScore = s[k].Score
	}
	return s
}

func (s StoreInfos) buildParameters(traceInfo *traceinfo.TraceInfo, predictConfig *traceinfo.PredictConfig, predictDefaultParameters map[string]interface{}) map[string]interface{} {
	parameters := make(map[string]interface{})
	if predictConfig.IntentExp {
		if len(traceInfo.QPResult.QueryStoreIntention) > 0 {
			for k, v := range predictConfig.IntentExpParametersMap["store"] {
				parameters[k] = v
			}
		} else if len(traceInfo.QPResult.QueryDishIntention) > 0 {
			for k, v := range predictConfig.IntentExpParametersMap["dish"] {
				parameters[k] = v
			}
		} else {
			for k, v := range predictConfig.IntentExpParametersMap["unknown"] {
				parameters[k] = v
			}
		}
	} else if predictConfig.UserExp {
		if predictConfig.UserExpType < len(traceInfo.UserContext.UserLevel) {
			if _, ok := predictConfig.UserExpParametersMap[traceInfo.UserContext.UserLevel[predictConfig.UserExpType]]; ok {
				for k, v := range predictConfig.UserExpParametersMap[traceInfo.UserContext.UserLevel[predictConfig.UserExpType]] {
					parameters[k] = v
				}
			} else {
				for k, v := range predictConfig.UserExpParametersMap["unknown"] {
					parameters[k] = v
				}
			}
		} else {
			for k, v := range predictConfig.UserExpParametersMap["unknown"] {
				parameters[k] = v
			}
		}
	} else {
		for k, v := range predictDefaultParameters {
			parameters[k] = v
		}
	}
	return parameters
}

// ltr 模型前，需要先用CoarseSortExpString计算得到fusion score, 排序再用ExpString结合ltr分数计算得到rerankscore
func (s StoreInfos) CalculateReRankScore(ctx context.Context, traceInfo *traceinfo.TraceInfo, isCoarseSort bool) StoreInfos {
	// 取消并发计算
	zeroValueOfCtrNum := 0
	zeroValueOfCvrNum := 0
	zeroValueOfRelNum := 0
	zeroValueOfDistNum := 0
	zeroValueOfPriceNum := 0
	zeroValueOfUENum := 0
	zeroValueOfUERatioNum := 0

	sumOfCtr := 0.0
	sumOfCvr := 0.0
	sumOfRel := 0.0
	sumOfDis := 0.0
	sumOfPrice := 0.0
	sumOfUe := 0.0
	sumOfUeRatio := 0.0
	sum := 0.0
	expStr := traceInfo.PredictConfig.ExpString
	paramsMap := traceInfo.PredictConfig.ExpParametersMap
	if isCoarseSort {
		expStr = traceInfo.PredictConfig.CoarseSortExpString
		paramsMap = traceInfo.PredictConfig.CoarseSortExpParametersMap
	}

	parameters := s.buildParameters(traceInfo, traceInfo.PredictConfig, paramsMap)
	if traceInfo.PredictConfig.UsePcFactor {
		pcFactor := pcfactor.PcFactorDict.GetPcFactor(traceInfo.PredictConfig.AbTestVal)
		parameters["w3"] = pcFactor.W3
		parameters["w4"] = pcFactor.W4
		parameters["w5"] = pcFactor.W5
	}
	if (traceInfo.CtrModelInfo.IsNeedPredict && traceInfo.CtrModelInfo.IsPredictSuccess == true) ||
		(traceInfo.CvrModelInfo.IsNeedPredict && traceInfo.CvrModelInfo.IsPredictSuccess == true) ||
		(traceInfo.RelModelInfo.IsNeedPredict && traceInfo.RelModelInfo.IsPredictSuccess == true) {
		traceInfo.Expression = util.BuildExpression(ctx, expStr)
	}
	isSegmentTopN := decision.IsStoreSegmentBoostTopN(traceInfo)
	for _, store := range s {
		score := store.GetReRankScore(ctx, traceInfo.Expression, traceInfo.PredictConfig, parameters, isSegmentTopN)
		if isCoarseSort {
			store.FusionScore = score
		}
		sum += score
		if store.PCtrScore == 0.0 {
			zeroValueOfCtrNum += 1
		}
		sumOfCtr += store.PCtrScore

		if store.PCvrScore == 0.0 {
			zeroValueOfCvrNum += 1
		}
		sumOfCvr += store.PCvrScore

		if store.GetRelevanceScore(traceInfo.PredictConfig.Relevance) == 0.0 {
			zeroValueOfRelNum += 1
		}
		sumOfRel += store.GetRelevanceScore(traceInfo.PredictConfig.Relevance)

		if store.DistanceScore == 0.0 {
			zeroValueOfDistNum += 1
		}
		sumOfDis += store.DistanceScore

		if store.GetPriceScore(traceInfo.PredictConfig.PriceType) == 0.0 {
			zeroValueOfPriceNum += 1
		}
		sumOfPrice += store.GetPriceScore(traceInfo.PredictConfig.PriceType)

		if store.GetStoreUE(traceInfo.PredictConfig.UEDefaultVal) == 0.0 {
			zeroValueOfUENum += 1
		}
		sumOfUe += store.GetStoreUE(traceInfo.PredictConfig.UEDefaultVal)

		if store.GetStoreUERadio(traceInfo.PredictConfig.UERadioDefaultVal) == 0.0 {
			zeroValueOfUERatioNum += 1
		}
		sumOfUeRatio += store.GetStoreUERadio(traceInfo.PredictConfig.UERadioDefaultVal)
	}

	metric_reporter.ReportCounter("zero_val_of_ctr", float64(zeroValueOfCtrNum), reporter.Label{Key: "ctr", Val: "ctr"})
	metric_reporter.ReportCounter("zero_val_of_cvr", float64(zeroValueOfCvrNum), reporter.Label{Key: "cvr", Val: "cvr"})
	metric_reporter.ReportCounter("zero_val_of_rel", float64(zeroValueOfRelNum), reporter.Label{Key: "rel", Val: "rel"})
	metric_reporter.ReportCounter("zero_val_of_dis", float64(zeroValueOfDistNum), reporter.Label{Key: "dis", Val: "dis"})
	metric_reporter.ReportCounter("zero_val_of_price", float64(zeroValueOfPriceNum), reporter.Label{Key: "price", Val: "price"})
	metric_reporter.ReportCounter("zero_val_of_ue", float64(zeroValueOfUENum), reporter.Label{Key: "ue", Val: "ue"})
	metric_reporter.ReportCounter("zero_val_of_ue_ratio", float64(zeroValueOfUERatioNum), reporter.Label{Key: "ue_ratio", Val: "ue_ratio"})

	nLen := len(s)
	if nLen > 0 {
		fLen := float64(nLen)
		metric_reporter.ReportCounter("mean_val_of_ctr", math.Abs(sumOfCtr/fLen), reporter.Label{Key: "ctr", Val: "ctr"})
		metric_reporter.ReportCounter("mean_val_of_cvr", math.Abs(sumOfCvr/fLen), reporter.Label{Key: "cvr", Val: "cvr"})
		metric_reporter.ReportCounter("mean_val_of_rel", math.Abs(sumOfRel/fLen), reporter.Label{Key: "rel", Val: "rel"})
		metric_reporter.ReportCounter("mean_val_of_dis", math.Abs(sumOfDis/fLen), reporter.Label{Key: "dis", Val: "dis"})
		metric_reporter.ReportCounter("mean_val_of_price", math.Abs(sumOfPrice/fLen), reporter.Label{Key: "price", Val: "price"})
		metric_reporter.ReportCounter("mean_val_of_ue", math.Abs(sumOfUe/fLen), reporter.Label{Key: "ue", Val: "ue"})
		metric_reporter.ReportCounter("mean_val_of_ue_ratio", math.Abs(sumOfUeRatio/fLen), reporter.Label{Key: "ue_ratio", Val: "ue_ratio"})
		metric_reporter.ReportCounter("mean_val_of_sum", math.Abs(sum/fLen), reporter.Label{Key: "sum", Val: "sum"})
	}

	scoreList := traceInfo.PredictConfig.DyFactorList
	if len(scoreList) == 6 {
		metric_reporter.ReportCounter("ltr_value1", math.Abs(scoreList[0]), reporter.Label{Key: "ltr", Val: "w1"})
		metric_reporter.ReportCounter("ltr_value2", math.Abs(scoreList[1]), reporter.Label{Key: "ltr", Val: "w2"})
		metric_reporter.ReportCounter("ltr_value3", math.Abs(scoreList[2]), reporter.Label{Key: "ltr", Val: "w3"})
		metric_reporter.ReportCounter("ltr_value4", math.Abs(scoreList[3]), reporter.Label{Key: "ltr", Val: "w4"})
		metric_reporter.ReportCounter("ltr_value5", math.Abs(scoreList[4]), reporter.Label{Key: "ltr", Val: "w5"})
		metric_reporter.ReportCounter("ltr_value6", math.Abs(scoreList[5]), reporter.Label{Key: "ltr", Val: "w6"})
	}
	return s
}

func (s StoreInfos) CalculateNotRelTabScore(ctx context.Context, traceInfo *traceinfo.TraceInfo) StoreInfos {
	expString := abtest.GetNotRelevanceTabExp(traceInfo.IsDebug, traceInfo.AbParamClient)
	if len(expString) == 0 {
		return s
	}
	expression := util.BuildExpression(ctx, expString)
	// 公式计算
	parameters := make(map[string]interface{})
	for _, store := range s {
		// 填充因子
		store.BuildStoreParams(traceInfo.PredictConfig, parameters)
		score, _ := util.EvaluateScore(ctx, expression, parameters)
		store.NotRelTabScore = score
	}
	return s
}
